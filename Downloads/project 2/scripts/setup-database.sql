-- Setup script for pose analysis database
-- Run this in your Supabase SQL Editor

-- First, create the storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

-- Then run the migration file
-- Copy and paste the contents of supabase/migrations/20250521120000_pose_analysis_tables.sql

-- Verify the setup
SELECT
  'Tables created successfully' as status,
  COUNT(*) as table_count
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('pose_videos', 'pose_sessions', 'pose_data', 'pose_metrics', 'pose_recommendations');

-- Check storage bucket
SELECT
  'Storage bucket ready' as status,
  name,
  public
FROM storage.buckets
WHERE id = 'videos';
