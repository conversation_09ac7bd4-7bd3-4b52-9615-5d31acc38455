/*
  # Pose Analysis Data Storage Schema

  This migration creates tables for storing pose analysis data for offline processing
  and data science analysis.
*/

-- Create pose_videos table to track uploaded videos
CREATE TABLE IF NOT EXISTS pose_videos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

  -- Video metadata
  filename TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size BIGINT,
  duration REAL, -- in seconds
  width INTEGER,
  height INTEGER,
  fps REAL,

  -- Analysis metadata
  activity_type TEXT NOT NULL CHECK (activity_type IN ('running', 'cycling')),
  view_type TEXT NOT NULL CHECK (view_type IN ('side', 'rear')),

  -- Processing status
  processing_status TEXT DEFAULT 'uploaded' CHECK (processing_status IN ('uploaded', 'processing', 'completed', 'failed')),
  processing_started_at TIMESTAMP WITH TIME ZONE,
  processing_completed_at TIMESTAMP WITH TIME ZONE,
  processing_error TEXT,

  -- User/session info (for future multi-user support)
  user_id UUID,
  session_id TEXT
);

-- Create pose_sessions table for tracking analysis runs
CREATE TABLE IF NOT EXISTS pose_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

  video_id UUID REFERENCES pose_videos(id) ON DELETE CASCADE,

  -- Analysis configuration
  model_type TEXT NOT NULL DEFAULT 'BlazePose', -- BlazePose, MoveNet, etc.
  model_version TEXT,
  analysis_fps REAL DEFAULT 10, -- frames per second analyzed

  -- Analysis results summary
  total_frames_analyzed INTEGER,
  successful_detections INTEGER,
  detection_rate REAL, -- percentage of successful detections

  -- Overall metrics
  avg_hip_angle REAL,
  avg_knee_angle REAL,
  avg_ankle_angle REAL,
  avg_trunk_angle REAL,
  avg_neck_angle REAL,
  avg_stride_length REAL,
  avg_posture_score REAL,

  -- Analysis metadata
  processing_time_seconds REAL,
  analysis_notes TEXT
);

-- Create pose_data table for storing frame-by-frame pose data
CREATE TABLE IF NOT EXISTS pose_data (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

  session_id UUID REFERENCES pose_sessions(id) ON DELETE CASCADE,

  -- Frame metadata
  frame_number INTEGER NOT NULL,
  timestamp_seconds REAL NOT NULL, -- timestamp in video

  -- Detection confidence
  detection_confidence REAL,
  pose_detected BOOLEAN DEFAULT false,

  -- Joint angles (in degrees)
  hip_angle REAL,
  knee_angle REAL,
  ankle_angle REAL,
  trunk_angle REAL,
  neck_angle REAL,

  -- Joint positions (normalized 0-100)
  hip_x REAL,
  hip_y REAL,
  knee_x REAL,
  knee_y REAL,
  ankle_x REAL,
  ankle_y REAL,
  trunk_x REAL,
  trunk_y REAL,
  neck_x REAL,
  neck_y REAL,

  -- Calculated metrics for this frame
  stride_length REAL,
  foot_strike_type TEXT CHECK (foot_strike_type IN ('heel', 'midfoot', 'forefoot')),
  posture_score REAL,

  -- Raw keypoint data (JSON for flexibility)
  raw_keypoints JSONB,

  UNIQUE(session_id, frame_number)
);

-- Create pose_metrics table for storing calculated performance data
CREATE TABLE IF NOT EXISTS pose_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

  session_id UUID REFERENCES pose_sessions(id) ON DELETE CASCADE,

  -- Metric type and category
  metric_name TEXT NOT NULL, -- e.g., 'stride_consistency', 'knee_drive', 'cadence'
  metric_category TEXT NOT NULL, -- e.g., 'efficiency', 'form', 'power'

  -- Metric values
  value REAL NOT NULL,
  unit TEXT, -- e.g., 'degrees', 'meters', 'percentage'
  score INTEGER CHECK (score >= 0 AND score <= 100), -- 0-100 score

  -- Analysis details
  calculation_method TEXT,
  confidence_level REAL,
  notes TEXT,

  -- Time range this metric applies to
  start_time_seconds REAL,
  end_time_seconds REAL
);

-- Create pose_recommendations table for storing AI-generated recommendations
CREATE TABLE IF NOT EXISTS pose_recommendations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

  session_id UUID REFERENCES pose_sessions(id) ON DELETE CASCADE,

  -- Recommendation details
  category TEXT NOT NULL, -- e.g., 'form', 'equipment', 'training'
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),

  title TEXT NOT NULL,
  description TEXT NOT NULL,

  -- For shoe/equipment recommendations
  equipment_type TEXT, -- e.g., 'running_shoes', 'bike_fit', 'insoles'
  specific_recommendations JSONB, -- structured data for specific product recommendations

  -- Supporting data
  supporting_metrics JSONB, -- which metrics led to this recommendation
  confidence_score REAL CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_pose_videos_activity_view ON pose_videos(activity_type, view_type);
CREATE INDEX IF NOT EXISTS idx_pose_videos_processing_status ON pose_videos(processing_status);
CREATE INDEX IF NOT EXISTS idx_pose_data_session_timestamp ON pose_data(session_id, timestamp_seconds);
CREATE INDEX IF NOT EXISTS idx_pose_data_frame_number ON pose_data(session_id, frame_number);
CREATE INDEX IF NOT EXISTS idx_pose_metrics_session ON pose_metrics(session_id, metric_category);
CREATE INDEX IF NOT EXISTS idx_pose_recommendations_session ON pose_recommendations(session_id, category, priority);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for pose_videos table
CREATE TRIGGER update_pose_videos_updated_at BEFORE UPDATE ON pose_videos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) for future multi-user support
ALTER TABLE pose_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE pose_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE pose_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE pose_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE pose_recommendations ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (can be restricted later)
CREATE POLICY "Enable read access for all users" ON pose_videos FOR SELECT USING (true);
CREATE POLICY "Enable insert access for all users" ON pose_videos FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update access for all users" ON pose_videos FOR UPDATE USING (true);

CREATE POLICY "Enable read access for all users" ON pose_sessions FOR SELECT USING (true);
CREATE POLICY "Enable insert access for all users" ON pose_sessions FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update access for all users" ON pose_sessions FOR UPDATE USING (true);

CREATE POLICY "Enable read access for all users" ON pose_data FOR SELECT USING (true);
CREATE POLICY "Enable insert access for all users" ON pose_data FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable read access for all users" ON pose_metrics FOR SELECT USING (true);
CREATE POLICY "Enable insert access for all users" ON pose_metrics FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable read access for all users" ON pose_recommendations FOR SELECT USING (true);
CREATE POLICY "Enable insert access for all users" ON pose_recommendations FOR INSERT WITH CHECK (true);
