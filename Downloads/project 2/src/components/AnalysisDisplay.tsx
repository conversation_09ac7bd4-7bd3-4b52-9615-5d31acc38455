import React, { useState, useRef, useEffect } from 'react';
import { ActivityType, AnalysisResults } from '../types';
import { Play, Pause, RotateCcw, AlertCircle } from 'lucide-react';
import MetricCard from './MetricCard';
import SkeletalOverlay from './SkeletalOverlay';
import { motion } from 'framer-motion';
import { validateVideoFormat } from '../utils/videoValidator';

interface AnalysisDisplayProps {
  activity: ActivityType;
  results: AnalysisResults | null;
  uploadedVideos: Record<string, string>;
}

const AnalysisDisplay: React.FC<AnalysisDisplayProps> = ({
  activity,
  results,
  uploadedVideos
}) => {
  console.log('AnalysisDisplay rendered with:', { activity, results, uploadedVideos });

  // Bail out early if no results
  if (!results) {
    console.error('No analysis results provided to AnalysisDisplay');
    return <div className="text-red-500 p-4 text-center">Error: No analysis results available</div>;
  }

  const [currentView, setCurrentView] = useState<'side' | 'rear'>('side');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Define videoSource here, after all state declarations but before any useEffects
  const videoSource = uploadedVideos[currentView];

  // Basic logging for video source
  console.log('AnalysisDisplay - Current view:', currentView);
  console.log('AnalysisDisplay - Current video source:', videoSource);

  // Bail out if no video source
  if (!videoSource) {
    console.error('No video source found for view:', currentView);
    return <div className="text-red-500 p-4 text-center">No video available for {currentView} view</div>;
  }

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = async () => {
      try {
        // Check if video source is valid
        if (!video.src) {
          setError('No video source provided');
          setIsLoading(false);
          return;
        }

        console.log(`Video loaded: ${video.src}`);
        console.log(`Video dimensions: ${video.videoWidth}x${video.videoHeight}`);
        console.log(`Video duration: ${video.duration}s`);

        // Simplified validation - just check if the video has dimensions and duration
        if (video.videoWidth === 0 || video.videoHeight === 0) {
          setError('Invalid video dimensions. The video may be corrupted.');
          setIsLoading(false);
          return;
        }

        // Set duration and complete loading
        setDuration(video.duration);
        setIsLoading(false);
        setError(null);
      } catch (err) {
        console.error('Video loading error:', err);
        setError('Error loading video. Please try again.');
        setIsLoading(false);
      }
    };

    const handleError = (e: Event) => {
      const videoElement = e.target as HTMLVideoElement;
      let errorMessage = 'An error occurred while loading the video';

      if (videoElement.error) {
        console.error('Video error code:', videoElement.error.code);
        console.error('Video error message:', videoElement.error.message);

        switch (videoElement.error.code) {
          case MediaError.MEDIA_ERR_ABORTED:
            errorMessage = 'Video playback was aborted';
            break;
          case MediaError.MEDIA_ERR_NETWORK:
            errorMessage = 'A network error occurred while loading the video. Please check your internet connection and try again.';
            break;
          case MediaError.MEDIA_ERR_DECODE:
            errorMessage = 'The video file is corrupted or uses an unsupported codec. Try converting your video to MP4 with H.264 encoding.';
            break;
          case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'The video format is not supported by your browser. Supported formats are:\n\n' +
                         '• MP4 (H.264 codec)\n' +
                         '• WebM (VP8/VP9 codec)\n' +
                         '• MOV (H.264 codec)\n' +
                         '• AVI (with proper codec)';
            break;
          default:
            errorMessage = videoElement.error.message || 'An unknown error occurred while loading the video';
        }
      }

      console.error('Video loading error:', errorMessage);
      setError(errorMessage);
      setIsLoading(false);

      // Try to recover by attempting to play without validation
      if (videoElement.src) {
        console.log('Attempting to recover playback...');
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
    };

    // Clean up any existing event listeners before adding new ones
    video.removeEventListener('loadedmetadata', handleLoadedMetadata);
    video.removeEventListener('error', handleError);
    video.removeEventListener('timeupdate', handleTimeUpdate);

    // Add event listeners
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('error', handleError);
    video.addEventListener('timeupdate', handleTimeUpdate);

    // Reset video state when source changes
    setIsLoading(true);
    setError(null);

    // Test video support immediately
    if (video.canPlayType) {
      const canPlayMP4 = video.canPlayType('video/mp4; codecs="avc1.42E01E"');
      const canPlayWebM = video.canPlayType('video/webm; codecs="vp8, vorbis"');
      const canPlayMOV = video.canPlayType('video/quicktime');
      const canPlayAVI = video.canPlayType('video/x-msvideo');

      console.log('Browser video format support:');
      console.log('- MP4 (H.264):', canPlayMP4 || 'no');
      console.log('- WebM:', canPlayWebM || 'no');
      console.log('- MOV:', canPlayMOV || 'no');
      console.log('- AVI:', canPlayAVI || 'no');

      if (!canPlayMP4 && !canPlayWebM && !canPlayMOV && !canPlayAVI) {
        setError('Your browser does not support the required video formats. Please try using a modern browser like Chrome, Firefox, or Safari.');
        setIsLoading(false);
      }
    }

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('error', handleError);
      video.removeEventListener('timeupdate', handleTimeUpdate);
    };
  }, [currentView, uploadedVideos]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const playVideo = async () => {
      try {
        // Check if the video is ready to play
        if (video.readyState < 2) {
          console.log('Video not ready to play yet (readyState =', video.readyState, ')');

          // Set a timeout to try again in a moment
          setTimeout(() => {
            if (isPlaying) {
              console.log('Retrying playback...');
              playVideo();
            }
          }, 500);
          return;
        }

        // Check if the video is an iPhone MOV file
        const isIPhoneVideo = videoSource?.toLowerCase().includes('.mov') ||
                             videoSource?.includes('IMG_');

        if (isIPhoneVideo) {
          console.log('iPhone video detected, applying special playback handling');
          // For iPhone videos, always start muted (can be unmuted by user later)
          video.muted = true;
          // Add additional attributes that help with iPhone video playback
          video.playsInline = true;
          video.setAttribute('playsinline', '');
          video.setAttribute('webkit-playsinline', '');
          // Force the video to reload with these settings
          video.load();
        }

        console.log('Attempting to play video...');
        await video.play();
        console.log('Video playing successfully');
      } catch (error) {
        console.error('Error playing video:', error);

        // Try with more permissive settings
        try {
          console.log('Trying with more permissive playback settings');
          video.muted = true;
          video.playsInline = true;
          video.setAttribute('playsinline', '');
          video.setAttribute('webkit-playsinline', '');
          // Add crossorigin attribute which can help with some CORS issues
          video.setAttribute('crossorigin', 'anonymous');

          // Force reload before trying to play again
          video.load();

          // Short delay before trying to play again
          await new Promise(resolve => setTimeout(resolve, 500));

          await video.play();
          console.log('Video playing with permissive settings');
        } catch (secondError) {
          console.error('Failed to play video even with permissive settings:', secondError);
          setIsPlaying(false);
          setError('Unable to play the video. Try clicking the play button or check if your browser supports this video format.');
        }
      }
    };

    if (isPlaying) {
      playVideo();
    } else {
      console.log('Pausing video');
      video.pause();
    }

    return () => {
      // Clean up by pausing the video when component unmounts or effect re-runs
      if (video) {
        console.log('Cleanup: pausing video');
        video.pause();
      }
    };
  }, [isPlaying, videoSource]);

  // videoSource is already defined at the top of the component

  const metrics = Object.entries(results.metrics).map(([key, metric]: [string, any]) => ({
    id: key,
    ...metric
  }));

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (videoRef.current) {
      const time = parseFloat(e.target.value);
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 p-6"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg max-w-md">
          <div className="flex items-start">
            <AlertCircle className="w-6 h-6 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Video Error
              </h3>
              <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line mb-4">{error}</p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p className="font-medium mb-2">Video Requirements:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>MP4 format with H.264 encoding</li>
                  <li>Maximum 10 seconds duration</li>
                  <li>Maximum file size: 100MB</li>
                  <li>Good lighting and clear view</li>
                </ul>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <p className="font-medium mb-2 text-gray-700 dark:text-gray-300">iPhone Video Troubleshooting:</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  iPhone .MOV files sometimes need conversion for web playback. Try these options:
                </p>
                <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <li>Use the Photos app to trim the video slightly and save as new clip</li>
                  <li>Use a free online converter to convert to MP4 format</li>
                  <li>Email the video to yourself (which often converts the format)</li>
                </ul>
                <div className="flex flex-col space-y-2">
                  <button
                    className="w-full py-2 px-4 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-dark transition-colors"
                    onClick={() => {
                      console.log('Attempting to reload video with different settings');
                      if (videoRef.current) {
                        // Try to reload with different settings
                        videoRef.current.muted = true;
                        videoRef.current.playsInline = true;
                        videoRef.current.load();

                        // Try to play after a short delay
                        setTimeout(() => {
                          videoRef.current?.play().catch(e => console.error('Play attempt failed:', e));
                        }, 500);
                      }
                    }}
                  >
                    Try Again with Different Settings
                  </button>

                  <a
                    href={videoSource}
                    download
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full py-2 px-4 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md text-sm font-medium text-center hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  >
                    Download Video
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="grid lg:grid-cols-5 gap-6">
        <div className="lg:col-span-3">
          <div className="analysis-container bg-white dark:bg-gray-800 p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                {activity === 'running' ? 'Running' : 'Cycling'} Analysis
              </h2>

              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentView('side')}
                  className={`px-3 py-1 text-sm rounded-md ${
                    currentView === 'side'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                >
                  Side View
                </button>
                <button
                  onClick={() => setCurrentView('rear')}
                  className={`px-3 py-1 text-sm rounded-md ${
                    currentView === 'rear'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                >
                  Rear View
                </button>
              </div>
            </div>

            <div className="relative bg-gray-900 rounded-md overflow-hidden mb-4" style={{ aspectRatio: '9/16', maxHeight: '70vh', minHeight: '400px' }}>
              {isLoading && (
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent mb-4"></div>
                  <button
                    className="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-dark transition-colors"
                    onClick={() => {
                      console.log('Manual video load triggered');
                      if (videoRef.current) {
                        // Force reload the video
                        videoRef.current.load();
                        // Try to play after a short delay
                        setTimeout(() => {
                          if (videoRef.current) {
                            videoRef.current.muted = true;
                            videoRef.current.play().catch(e => console.error('Manual play attempt failed:', e));
                            setIsPlaying(true);
                          }
                        }, 500);
                      }
                    }}
                  >
                    Click to Load Video
                  </button>
                </div>
              )}

              {renderError()}

              <video
                ref={videoRef}
                className="w-full h-full object-contain"
                loop
                playsInline
                webkit-playsinline="true"
                x-webkit-airplay="allow"
                autoPlay={false}
                muted={true} // Start muted to help with autoplay restrictions
                controls={false} // Use custom controls
                preload="auto" // Change from metadata to auto for faster loading
                crossOrigin="anonymous" // Required for pose detection to access video pixels

                src={videoSource} // Use direct src for simplicity
                onLoadStart={() => {
                  setIsLoading(true);
                }}
                onCanPlay={() => {
                  setIsLoading(false);
                }}
                onError={(e) => {
                  console.error('Video loading error:', e);
                  const target = e.target as HTMLVideoElement;
                  if (target.error) {
                    console.error('Error code:', target.error.code, 'Message:', target.error.message);
                  }
                  setError('Error loading video. Please try refreshing the page.');
                  setIsLoading(false);
                }}
              >
                <p>Your browser doesn't support HTML5 video. Please use a modern browser.</p>
              </video>

              <SkeletalOverlay
                activity={activity}
                view={currentView}
                currentTime={currentTime}
                duration={duration}
                videoRef={videoRef}
                sessionId={results.sessionId}
              />



              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 px-4 py-2">
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setIsPlaying(!isPlaying)}
                      className="hover:text-primary transition"
                      disabled={isLoading || !!error}
                    >
                      {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                    </button>

                    <span className="text-sm">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                  </div>

                  <div className="flex-1 mx-4">
                    <input
                      type="range"
                      min="0"
                      max={duration || 100}
                      value={currentTime}
                      onChange={handleSeek}
                      className="w-full"
                      disabled={isLoading || !!error}
                    />
                  </div>

                  <button
                    onClick={() => {
                      if (videoRef.current) {
                        videoRef.current.currentTime = 0;
                      }
                    }}
                    className="hover:text-primary transition"
                    disabled={isLoading || !!error}
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="analysis-container bg-white dark:bg-gray-800 p-6 mb-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                Overall Score
              </h3>
              <div className="relative h-20 w-20">
                <svg className="w-full h-full" viewBox="0 0 36 36">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#E5E7EB"
                    strokeWidth="3"
                    strokeDasharray="100, 100"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke={results.overallScore >= 80 ? '#35AE7C' : '#FF7846'}
                    strokeWidth="3"
                    strokeDasharray={`${results.overallScore}, 100`}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center text-xl font-bold">
                  {results.overallScore}%
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {metrics.map((metric) => (
                <MetricCard key={metric.id} metric={metric} />
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      <div className="analysis-container bg-white dark:bg-gray-800 p-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Analysis Summary
        </h3>

        <div className="space-y-4 text-gray-600 dark:text-gray-300">
          {activity === 'running' ? (
            <>
              <p>
                Your running form shows good overall mechanics with a score of {results.overallScore}%.
                The analysis reveals good midfoot striking and consistent stride length, but there is
                room for improvement in your posture.
              </p>

              <p>Key observations:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Knee flexion angle of {results.jointAngles.knee}° is within optimal range</li>
                <li>Slight overpronation detected in foot landing</li>
                <li>Forward lean could be reduced slightly for better form</li>
                <li>Good hip mobility throughout your stride</li>
              </ul>

              <p className="italic text-sm mt-6">
                Note: This is a preliminary analysis. For comprehensive biomechanical diagnostics,
                consider scheduling a session with a professional running coach.
              </p>
            </>
          ) : (
            <>
              <p>
                Your cycling posture shows good overall biomechanics with a score of{' '}
                {results.overallScore}%. The analysis indicates good power transfer mechanics, with
                minor adjustments needed to saddle height.
              </p>

              <p>Key observations:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Knee extension angle of {results.jointAngles.knee}° is near optimal range</li>
                <li>Hip angle of {results.jointAngles.hip}° allows efficient power transfer</li>
                <li>Torso angle provides good balance between aerodynamics and comfort</li>
                <li>Consider raising saddle height by 1-2cm for improved efficiency</li>
              </ul>

              <p className="italic text-sm mt-6">
                Note: This is a preliminary analysis. For comprehensive bike fitting, consider
                scheduling a session with a professional bike fitter.
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalysisDisplay;