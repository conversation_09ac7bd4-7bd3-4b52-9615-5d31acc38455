import React, { useState, useEffect } from 'react';
import { ActivityType } from '../types';

interface PoseDataPoint {
  frame_number: number;
  timestamp_seconds: number;
  pose_detected: boolean;
  detection_confidence: number;
  hip_angle: number;
  knee_angle: number;
  ankle_angle: number;
  trunk_angle: number;
  neck_angle: number;
  hip_x: number;
  hip_y: number;
  knee_x: number;
  knee_y: number;
  ankle_x: number;
  ankle_y: number;
  trunk_x: number;
  trunk_y: number;
  neck_x: number;
  neck_y: number;
  // Extended keypoints for comprehensive skeleton
  shoulder_x?: number;
  shoulder_y?: number;
  elbow_x?: number;
  elbow_y?: number;
  wrist_x?: number;
  wrist_y?: number;
  foot_x?: number;
  foot_y?: number;
}

interface SkeletalOverlayProps {
  activity: ActivityType;
  view: 'side' | 'rear';
  currentTime: number;
  duration: number;
  videoRef: React.RefObject<HTMLVideoElement>;
  sessionId?: string;
}

interface Joint {
  x: number;
  y: number;
  confidence: number;
}

interface SkeletonJoints {
  nose: Joint;
  neck: Joint;
  trunk: Joint;
  hip: Joint;
  knee: Joint;
  ankle: Joint;
  foot: Joint;
  shoulder: Joint;
  elbow: Joint;
  wrist: Joint;
  // Mirrored side
  hipMirrored: Joint;
  kneeMirrored: Joint;
  ankleMirrored: Joint;
  footMirrored: Joint;
  shoulderMirrored: Joint;
  elbowMirrored: Joint;
  wristMirrored: Joint;
}

const SkeletalOverlay: React.FC<SkeletalOverlayProps> = ({
  activity,
  view,
  currentTime,
  videoRef,
  sessionId
}) => {
  const [poseData, setPoseData] = useState<PoseDataPoint[]>([]);
  const [currentPose, setCurrentPose] = useState<PoseDataPoint | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load pre-processed pose data
  useEffect(() => {
    if (sessionId) {
      loadPoseData();
    }
  }, [sessionId]);

  // Update current pose with optimized binary search
  useEffect(() => {
    if (poseData.length > 0 && videoRef.current) {
      const currentVideoTime = currentTime;
      const closestPose = findClosestPose(poseData, currentVideoTime);
      
      if (!currentPose || closestPose.frame_number !== currentPose.frame_number) {
        setCurrentPose(closestPose);
      }
    }
  }, [currentTime, poseData, currentPose]);

  const findClosestPose = (data: PoseDataPoint[], targetTime: number): PoseDataPoint => {
    if (data.length === 0) return data[0];
    if (data.length === 1) return data[0];

    let left = 0;
    let right = data.length - 1;
    let closest = data[0];
    let minDiff = Math.abs(data[0].timestamp_seconds - targetTime);

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const diff = Math.abs(data[mid].timestamp_seconds - targetTime);

      if (diff < minDiff) {
        minDiff = diff;
        closest = data[mid];
      }

      if (data[mid].timestamp_seconds < targetTime) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }

    return closest;
  };

  const loadPoseData = async () => {
    if (!sessionId) return;

    setIsLoading(true);
    try {
      const { default: supabase } = await import('../utils/supabaseClient');

      const { data, error } = await supabase
        .from('pose_data')
        .select('*')
        .eq('session_id', sessionId)
        .order('frame_number');

      if (error) {
        setError(`Failed to load pose data: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        setError('No pose data found for this session');
        setPoseData([]);
      } else {
        setPoseData(data);
        setError(null);
      }
    } catch (err) {
      setError(`Failed to load pose data: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate internal joint angle between three points
  const calculateInternalAngle = (point1: Joint, vertex: Joint, point2: Joint): number => {
    const vector1 = {
      x: point1.x - vertex.x,
      y: point1.y - vertex.y
    };
    
    const vector2 = {
      x: point2.x - vertex.x,
      y: point2.y - vertex.y
    };

    const dot = vector1.x * vector2.x + vector1.y * vector2.y;
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);

    if (mag1 === 0 || mag2 === 0) return 0;

    const cosAngle = dot / (mag1 * mag2);
    const angleRad = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
    return angleRad * (180 / Math.PI);
  };

  // Auto-detect runner scale based on body proportions
  const calculateRunnerScale = (joints: SkeletonJoints): number => {
    const bodyHeight = Math.abs(joints.neck.y - joints.ankle.y);
    const expectedBodyHeight = 60; // Expected percentage of video height for optimal viewing
    return Math.max(0.5, Math.min(2.0, expectedBodyHeight / bodyHeight));
  };

  // Build comprehensive skeleton from pose data
  const buildSkeletonJoints = (pose: PoseDataPoint): SkeletonJoints => {
    const baseConfidence = pose.detection_confidence || 0.8;
    
    // Primary side joints (detected)
    const hip: Joint = { x: pose.hip_x ?? 50, y: pose.hip_y ?? 50, confidence: baseConfidence };
    const knee: Joint = { x: pose.knee_x ?? 50, y: pose.knee_y ?? 70, confidence: baseConfidence };
    const ankle: Joint = { x: pose.ankle_x ?? 50, y: pose.ankle_y ?? 90, confidence: baseConfidence };
    const trunk: Joint = { x: pose.trunk_x ?? 50, y: pose.trunk_y ?? 30, confidence: baseConfidence };
    const neck: Joint = { x: pose.neck_x ?? 50, y: pose.neck_y ?? 10, confidence: baseConfidence };

    // Estimated joints (can be enhanced with actual detection later)
    const shoulder: Joint = { 
      x: pose.shoulder_x ?? trunk.x + (hip.x - trunk.x) * 0.2, 
      y: pose.shoulder_y ?? trunk.y + (hip.y - trunk.y) * 0.3, 
      confidence: pose.shoulder_x ? baseConfidence : baseConfidence * 0.7 
    };
    
    const elbow: Joint = { 
      x: pose.elbow_x ?? shoulder.x + (hip.x - shoulder.x) * 0.8, 
      y: pose.elbow_y ?? shoulder.y + (hip.y - shoulder.y) * 0.6, 
      confidence: pose.elbow_x ? baseConfidence : baseConfidence * 0.6 
    };
    
    const wrist: Joint = { 
      x: pose.wrist_x ?? elbow.x + (hip.x - elbow.x) * 0.5, 
      y: pose.wrist_y ?? elbow.y + (hip.y - elbow.y) * 0.8, 
      confidence: pose.wrist_x ? baseConfidence : baseConfidence * 0.5 
    };
    
    const foot: Joint = { 
      x: pose.foot_x ?? ankle.x + (knee.x - ankle.x) * 0.3, 
      y: pose.foot_y ?? ankle.y + 5, 
      confidence: pose.foot_x ? baseConfidence : baseConfidence * 0.6 
    };

    const nose: Joint = { 
      x: neck.x, 
      y: neck.y - 8, 
      confidence: baseConfidence * 0.8 
    };

    // Calculate bilateral symmetry
    const centerX = trunk.x;
    const hipOffsetX = hip.x - centerX;
    const kneeOffsetX = knee.x - centerX;
    const ankleOffsetX = ankle.x - centerX;
    const shoulderOffsetX = shoulder.x - centerX;
    const elbowOffsetX = elbow.x - centerX;
    const wristOffsetX = wrist.x - centerX;
    const footOffsetX = foot.x - centerX;

    // Mirrored side joints
    const hipMirrored: Joint = { x: centerX - hipOffsetX, y: hip.y, confidence: baseConfidence * 0.7 };
    const kneeMirrored: Joint = { x: centerX - kneeOffsetX, y: knee.y, confidence: baseConfidence * 0.7 };
    const ankleMirrored: Joint = { x: centerX - ankleOffsetX, y: ankle.y, confidence: baseConfidence * 0.7 };
    const footMirrored: Joint = { x: centerX - footOffsetX, y: foot.y, confidence: baseConfidence * 0.6 };
    const shoulderMirrored: Joint = { x: centerX - shoulderOffsetX, y: shoulder.y, confidence: baseConfidence * 0.6 };
    const elbowMirrored: Joint = { x: centerX - elbowOffsetX, y: elbow.y, confidence: baseConfidence * 0.5 };
    const wristMirrored: Joint = { x: centerX - wristOffsetX, y: wrist.y, confidence: baseConfidence * 0.4 };

    return {
      nose, neck, trunk, hip, knee, ankle, foot, shoulder, elbow, wrist,
      hipMirrored, kneeMirrored, ankleMirrored, footMirrored, 
      shoulderMirrored, elbowMirrored, wristMirrored
    };
  };

  // Create angle arc aligned with actual bone segments
  const createAlignedAngleArc = (
    vertex: Joint, 
    point1: Joint, 
    point2: Joint, 
    angle: number, 
    radius: number = 8
  ): string => {
    // Calculate vectors from vertex to both points
    const vec1 = { x: point1.x - vertex.x, y: point1.y - vertex.y };
    const vec2 = { x: point2.x - vertex.x, y: point2.y - vertex.y };

    // Normalize vectors
    const mag1 = Math.sqrt(vec1.x * vec1.x + vec1.y * vec1.y);
    const mag2 = Math.sqrt(vec2.x * vec2.x + vec2.y * vec2.y);
    
    if (mag1 === 0 || mag2 === 0) return '';

    vec1.x /= mag1;
    vec1.y /= mag1;
    vec2.x /= mag2;
    vec2.y /= mag2;

    // Calculate start and end points for arc
    const arcStart = {
      x: vertex.x + vec1.x * radius,
      y: vertex.y + vec1.y * radius
    };
    
    const arcEnd = {
      x: vertex.x + vec2.x * radius,
      y: vertex.y + vec2.y * radius
    };

    // Determine sweep direction
    const cross = vec1.x * vec2.y - vec1.y * vec2.x;
    const sweepFlag = cross > 0 ? 1 : 0;
    const largeArcFlag = angle > 180 ? 1 : 0;

    return `M ${vertex.x} ${vertex.y} L ${arcStart.x} ${arcStart.y} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${arcEnd.x} ${arcEnd.y} Z`;
  };

  const renderRunningSkeletonSide = () => {
    if (!currentPose || !currentPose.pose_detected) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg">
            {isLoading ? 'Loading pose data...' : 'No pose detected'}
          </div>
        </div>
      );
    }

    const joints = buildSkeletonJoints(currentPose);
    const runnerScale = calculateRunnerScale(joints);
    
    // Calculate dynamic sizing based on video dimensions
    const videoElement = videoRef.current;
    const videoWidth = videoElement?.videoWidth || 1920;
    const videoHeight = videoElement?.videoHeight || 1080;
    const baseSize = Math.min(videoWidth, videoHeight);
    
    const strokeWidth = Math.max(0.3, Math.round(baseSize / 600) * runnerScale);
    const circleRadius = Math.max(1, Math.round(baseSize / 400) * runnerScale);
    const fontSize = Math.max(1.5, Math.round(baseSize / 350) * runnerScale);
    const arcRadius = Math.max(6, Math.round(baseSize / 180) * runnerScale);

    // Calculate accurate internal joint angles
    const hipAngle = calculateInternalAngle(joints.trunk, joints.hip, joints.knee);
    const kneeAngle = calculateInternalAngle(joints.hip, joints.knee, joints.ankle);
    const ankleAngle = calculateInternalAngle(joints.knee, joints.ankle, joints.foot);
    const shoulderAngle = calculateInternalAngle(joints.neck, joints.shoulder, joints.elbow);
    const elbowAngle = calculateInternalAngle(joints.shoulder, joints.elbow, joints.wrist);

    return (
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        viewBox="0 0 100 100"
        preserveAspectRatio="xMidYMid meet"
        style={{ zIndex: 50 }}
      >
        <defs>
          <filter id="glow">
            <feGaussianBlur stdDeviation="0.5" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="shadow">
            <feDropShadow dx="0.2" dy="0.2" stdDeviation="0.3" floodColor="black" floodOpacity="0.3"/>
          </filter>
        </defs>

        {/* COMPREHENSIVE SKELETON LINES */}
        <g className="skeleton-lines">
          {/* Central spine */}
          <line x1={joints.nose.x} y1={joints.nose.y} x2={joints.neck.x} y2={joints.neck.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.2} strokeLinecap="round" opacity="0.9"/>
          <line x1={joints.neck.x} y1={joints.neck.y} x2={joints.trunk.x} y2={joints.trunk.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.5} strokeLinecap="round" opacity="0.9"/>
          <line x1={joints.trunk.x} y1={joints.trunk.y} x2={joints.trunk.x} y2={joints.hip.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.5} strokeLinecap="round" opacity="0.9"/>

          {/* Hip connections */}
          <line x1={joints.trunk.x} y1={joints.hip.y} x2={joints.hip.x} y2={joints.hip.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.2} strokeLinecap="round" opacity="0.9"/>
          <line x1={joints.trunk.x} y1={joints.hip.y} x2={joints.hipMirrored.x} y2={joints.hipMirrored.y}
                stroke="#0080FF" strokeWidth={strokeWidth} strokeLinecap="round" opacity="0.7"/>

          {/* PRIMARY LEG (detected side) */}
          <line x1={joints.hip.x} y1={joints.hip.y} x2={joints.knee.x} y2={joints.knee.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.3} strokeLinecap="round" opacity="0.95"/>
          <line x1={joints.knee.x} y1={joints.knee.y} x2={joints.ankle.x} y2={joints.ankle.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.3} strokeLinecap="round" opacity="0.95"/>
          <line x1={joints.ankle.x} y1={joints.ankle.y} x2={joints.foot.x} y2={joints.foot.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.1} strokeLinecap="round" opacity="0.9"/>

          {/* SECONDARY LEG (mirrored side) */}
          <line x1={joints.hipMirrored.x} y1={joints.hipMirrored.y} x2={joints.kneeMirrored.x} y2={joints.kneeMirrored.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 0.8} strokeLinecap="round" opacity="0.7"/>
          <line x1={joints.kneeMirrored.x} y1={joints.kneeMirrored.y} x2={joints.ankleMirrored.x} y2={joints.ankleMirrored.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 0.8} strokeLinecap="round" opacity="0.7"/>
          <line x1={joints.ankleMirrored.x} y1={joints.ankleMirrored.y} x2={joints.footMirrored.x} y2={joints.footMirrored.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 0.7} strokeLinecap="round" opacity="0.6"/>

          {/* PRIMARY ARM (detected side) */}
          <line x1={joints.trunk.x} y1={joints.trunk.y} x2={joints.shoulder.x} y2={joints.shoulder.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.1} strokeLinecap="round" opacity={joints.shoulder.confidence}/>
          <line x1={joints.shoulder.x} y1={joints.shoulder.y} x2={joints.elbow.x} y2={joints.elbow.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 1.0} strokeLinecap="round" opacity={joints.elbow.confidence}/>
          <line x1={joints.elbow.x} y1={joints.elbow.y} x2={joints.wrist.x} y2={joints.wrist.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 0.9} strokeLinecap="round" opacity={joints.wrist.confidence}/>

          {/* SECONDARY ARM (mirrored side) */}
          <line x1={joints.trunk.x} y1={joints.trunk.y} x2={joints.shoulderMirrored.x} y2={joints.shoulderMirrored.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 0.8} strokeLinecap="round" opacity={joints.shoulderMirrored.confidence}/>
          <line x1={joints.shoulderMirrored.x} y1={joints.shoulderMirrored.y} x2={joints.elbowMirrored.x} y2={joints.elbowMirrored.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 0.7} strokeLinecap="round" opacity={joints.elbowMirrored.confidence}/>
          <line x1={joints.elbowMirrored.x} y1={joints.elbowMirrored.y} x2={joints.wristMirrored.x} y2={joints.wristMirrored.y}
                stroke="#0080FF" strokeWidth={strokeWidth * 0.6} strokeLinecap="round" opacity={joints.wristMirrored.confidence}/>
        </g>

        {/* ACCURATE ANGLE ARCS aligned with bone segments */}
        <g className="angle-arcs">
          {/* Hip angle arc - between trunk and thigh */}
          <path d={createAlignedAngleArc(joints.hip, joints.trunk, joints.knee, hipAngle, arcRadius)}
                fill="rgba(255,140,0,0.4)" stroke="rgba(255,140,0,0.9)" strokeWidth="0.8" opacity="0.9"/>
          
          {/* Knee angle arc - between thigh and shin */}
          <path d={createAlignedAngleArc(joints.knee, joints.hip, joints.ankle, kneeAngle, arcRadius)}
                fill="rgba(255,140,0,0.4)" stroke="rgba(255,140,0,0.9)" strokeWidth="0.8" opacity="0.9"/>
          
          {/* Ankle angle arc - between shin and foot */}
          <path d={createAlignedAngleArc(joints.ankle, joints.knee, joints.foot, ankleAngle, arcRadius * 0.8)}
                fill="rgba(255,140,0,0.4)" stroke="rgba(255,140,0,0.9)" strokeWidth="0.7" opacity="0.8"/>
          
          {/* Shoulder angle arc */}
          {joints.shoulder.confidence > 0.5 && (
            <path d={createAlignedAngleArc(joints.shoulder, joints.neck, joints.elbow, shoulderAngle, arcRadius * 0.7)}
                  fill="rgba(255,140,0,0.3)" stroke="rgba(255,140,0,0.8)" strokeWidth="0.6" opacity={joints.shoulder.confidence}/>
          )}
          
          {/* Elbow angle arc */}
          {joints.elbow.confidence > 0.5 && (
            <path d={createAlignedAngleArc(joints.elbow, joints.shoulder, joints.wrist, elbowAngle, arcRadius * 0.6)}
                  fill="rgba(255,140,0,0.3)" stroke="rgba(255,140,0,0.8)" strokeWidth="0.6" opacity={joints.elbow.confidence}/>
          )}
        </g>

        {/* COMPREHENSIVE JOINT MARKERS */}
        <g className="joint-markers">
          {/* Head/Neck */}
          <circle cx={joints.nose.x} cy={joints.nose.y} r={circleRadius * 0.8}
                  fill="#87CEEB" stroke="white" strokeWidth={strokeWidth * 0.3} opacity="0.9"/>
          <circle cx={joints.neck.x} cy={joints.neck.y} r={circleRadius}
                  fill="#0080FF" stroke="white" strokeWidth={strokeWidth * 0.3} opacity="0.9"/>
          
          {/* Torso */}
          <circle cx={joints.trunk.x} cy={joints.trunk.y} r={circleRadius * 1.1}
                  fill="#0080FF" stroke="white" strokeWidth={strokeWidth * 0.3} opacity="0.9"/>
          <circle cx={joints.trunk.x} cy={joints.hip.y} r={circleRadius * 0.9}
                  fill="#0080FF" stroke="white" strokeWidth={strokeWidth * 0.3} opacity="0.9"/>

          {/* Primary Leg */}
          <circle cx={joints.hip.x} cy={joints.hip.y} r={circleRadius * 1.2}
                  fill="#FF0000" stroke="white" strokeWidth={strokeWidth * 0.3} opacity="0.95"/>
          <circle cx={joints.knee.x} cy={joints.knee.y} r={circleRadius * 1.1}
                  fill="#FFFF00" stroke="white" strokeWidth={strokeWidth * 0.3} opacity="0.95"/>
          <circle cx={joints.ankle.x} cy={joints.ankle.y} r={circleRadius}
                  fill="#00FF00" stroke="white" strokeWidth={strokeWidth * 0.3} opacity="0.9"/>
          <circle cx={joints.foot.x} cy={joints.foot.y} r={circleRadius * 0.8}
                  fill="#00AA00" stroke="white" strokeWidth={strokeWidth * 0.2} opacity={joints.foot.confidence}/>

          {/* Secondary Leg */}
          <circle cx={joints.hipMirrored.x} cy={joints.hipMirrored.y} r={circleRadius * 0.9}
                  fill="#FF0000" stroke="white" strokeWidth={strokeWidth * 0.2} opacity="0.7"/>
          <circle cx={joints.kneeMirrored.x} cy={joints.kneeMirrored.y} r={circleRadius * 0.8}
                  fill="#FFFF00" stroke="white" strokeWidth={strokeWidth * 0.2} opacity="0.7"/>
          <circle cx={joints.ankleMirrored.x} cy={joints.ankleMirrored.y} r={circleRadius * 0.7}
                  fill="#00FF00" stroke="white" strokeWidth={strokeWidth * 0.2} opacity="0.6"/>
          <circle cx={joints.footMirrored.x} cy={joints.footMirrored.y} r={circleRadius * 0.6}
                  fill="#00AA00" stroke="white" strokeWidth={strokeWidth * 0.15} opacity="0.5"/>

          {/* Primary Arm */}
          <circle cx={joints.shoulder.x} cy={joints.shoulder.y} r={circleRadius * 0.9}
                  fill="#FF6600" stroke="white" strokeWidth={strokeWidth * 0.2} opacity={joints.shoulder.confidence}/>
          <circle cx={joints.elbow.x} cy={joints.elbow.y} r={circleRadius * 0.8}
                  fill="#FF9900" stroke="white" strokeWidth={strokeWidth * 0.2} opacity={joints.elbow.confidence}/>
          <circle cx={joints.wrist.x} cy={joints.wrist.y} r={circleRadius * 0.7}
                  fill="#FFCC00" stroke="white" strokeWidth={strokeWidth * 0.15} opacity={joints.wrist.confidence}/>

          {/* Secondary Arm */}
          <circle cx={joints.shoulderMirrored.x} cy={joints.shoulderMirrored.y} r={circleRadius * 0.7}
                  fill="#FF6600" stroke="white" strokeWidth={strokeWidth * 0.15} opacity={joints.shoulderMirrored.confidence}/>
          <circle cx={joints.elbowMirrored.x} cy={joints.elbowMirrored.y} r={circleRadius * 0.6}
                  fill="#FF9900" stroke="white" strokeWidth={strokeWidth * 0.15} opacity={joints.elbowMirrored.confidence}/>
          <circle cx={joints.wristMirrored.x} cy={joints.wristMirrored.y} r={circleRadius * 0.5}
                  fill="#FFCC00" stroke="white" strokeWidth={strokeWidth * 0.1} opacity={joints.wristMirrored.confidence}/>
        </g>

        {/* ENHANCED ANGLE LABELS positioned optimally */}
        <g className="angle-labels">
          {/* Hip angle label */}
          <g>
            <rect x={joints.hip.x - 15} y={joints.hip.y - 28} width="30" height="14" rx="3"
                  fill="rgba(255,140,0,0.95)" stroke="white" strokeWidth="0.3" filter="url(#shadow)"/>
            <text x={joints.hip.x} y={joints.hip.y - 18} textAnchor="middle" fill="white"
                  fontSize={fontSize * 1.1} fontWeight="bold">
              {Math.round(hipAngle)}°
            </text>
          </g>

          {/* Knee angle label */}
          <g>
            <rect x={joints.knee.x - 15} y={joints.knee.y + 20} width="30" height="14" rx="3"
                  fill="rgba(255,140,0,0.95)" stroke="white" strokeWidth="0.3" filter="url(#shadow)"/>
            <text x={joints.knee.x} y={joints.knee.y + 30} textAnchor="middle" fill="white"
                  fontSize={fontSize * 1.1} fontWeight="bold">
              {Math.round(kneeAngle)}°
            </text>
          </g>

          {/* Ankle angle label */}
          <g>
            <rect x={joints.ankle.x - 12} y={joints.ankle.y + 15} width="24" height="12" rx="2"
                  fill="rgba(255,140,0,0.9)" stroke="white" strokeWidth="0.2"/>
            <text x={joints.ankle.x} y={joints.ankle.y + 23} textAnchor="middle" fill="white"
                  fontSize={fontSize * 0.9} fontWeight="bold">
              {Math.round(ankleAngle)}°
            </text>
          </g>

          {/* Conditional arm angle labels */}
          {joints.shoulder.confidence > 0.6 && (
            <g>
              <rect x={joints.shoulder.x + 12} y={joints.shoulder.y - 8} width="24" height="12" rx="2"
                    fill="rgba(255,140,0,0.8)" stroke="white" strokeWidth="0.2"/>
              <text x={joints.shoulder.x + 24} y={joints.shoulder.y} textAnchor="middle" fill="white"
                    fontSize={fontSize * 0.8} fontWeight="bold">
                {Math.round(shoulderAngle)}°
              </text>
            </g>
          )}

          {joints.elbow.confidence > 0.6 && (
            <g>
              <rect x={joints.elbow.x + 10} y={joints.elbow.y - 6} width="20" height="10" rx="2"
                    fill="rgba(255,140,0,0.8)" stroke="white" strokeWidth="0.2"/>
              <text x={joints.elbow.x + 20} y={joints.elbow.y} textAnchor="middle" fill="white"
                    fontSize={fontSize * 0.7} fontWeight="bold">
                {Math.round(elbowAngle)}°
              </text>
            </g>
          )}
        </g>

        {/* ENHANCED DEBUG & METRICS OVERLAY */}
        <g className="debug-overlay">
          <rect x="2" y="2" width="38" height="32" rx="2"
                fill="rgba(0,0,0,0.85)" stroke="rgba(255,255,255,0.3)" strokeWidth="0.2"/>
          <text x="4" y="7" fill="white" fontSize="2.5" fontWeight="bold">
            Frame: {currentPose.frame_number}
          </text>
          <text x="4" y="11" fill="white" fontSize="2">
            Video: {currentTime.toFixed(2)}s
          </text>
          <text x="4" y="15" fill="white" fontSize="2">
            Pose: {currentPose.timestamp_seconds.toFixed(2)}s
          </text>
          <text x="4" y="19" fill={Math.abs(currentTime - currentPose.timestamp_seconds) > 0.1 ? "red" : "green"}
                fontSize="2" fontWeight="bold">
            Δ: {Math.abs(currentTime - currentPose.timestamp_seconds).toFixed(3)}s
          </text>
          <text x="4" y="23" fill="white" fontSize="2">
            Conf: {Math.round(currentPose.detection_confidence * 100)}%
          </text>
          <text x="4" y="27" fill="white" fontSize="2">
            Scale: {runnerScale.toFixed(2)}x
          </text>
          <text x="4" y="31" fill="white" fontSize="2">
            Poses: {poseData.length}
          </text>
        </g>

        {/* PERFORMANCE METRICS OVERLAY */}
        <g className="metrics-overlay">
          <rect x="2" y="36" width="32" height="20" rx="2"
                fill="rgba(0,0,0,0.75)" stroke="rgba(255,255,255,0.2)" strokeWidth="0.1"/>
          <text x="4" y="42" fill="white" fontSize="3" fontWeight="500">
            Hip: {Math.round(hipAngle)}°
          </text>
          <text x="4" y="46" fill="white" fontSize="3" fontWeight="500">
            Knee: {Math.round(kneeAngle)}°
          </text>
          <text x="4" y="50" fill="white" fontSize="3" fontWeight="500">
            Ankle: {Math.round(ankleAngle)}°
          </text>
          <text x="4" y="54" fill="white" fontSize="2.5" fontWeight="400">
            Quality: {Math.round(joints.hip.confidence * 100)}%
          </text>
        </g>
      </svg>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 20 }}>
      {error && (
        <div className="absolute top-2 left-2 bg-red-500 text-white px-3 py-2 rounded text-sm z-30">
          {error}
        </div>
      )}

      {activity === 'running' && view === 'side' && renderRunningSkeletonSide()}
    </div>
  );
};

export default SkeletalOverlay;