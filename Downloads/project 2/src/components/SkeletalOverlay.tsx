import React, { useState, useEffect } from 'react';
import { ActivityType } from '../types';

interface PoseDataPoint {
  frame_number: number;
  timestamp_seconds: number;
  pose_detected: boolean;
  detection_confidence: number;
  hip_angle: number;
  knee_angle: number;
  ankle_angle: number;
  trunk_angle: number;
  neck_angle: number;
  hip_x: number;
  hip_y: number;
  knee_x: number;
  knee_y: number;
  ankle_x: number;
  ankle_y: number;
  trunk_x: number;  // Changed from shoulder_x to match database
  trunk_y: number;  // Changed from shoulder_y to match database
  neck_x: number;
  neck_y: number;
}

interface SkeletalOverlayProps {
  activity: ActivityType;
  view: 'side' | 'rear';
  currentTime: number;
  duration: number;
  videoRef: React.RefObject<HTMLVideoElement>;
  sessionId?: string; // Optional session ID for pre-processed data
}

const SkeletalOverlay: React.FC<SkeletalOverlayProps> = ({
  activity,
  view,
  currentTime,
  videoRef,
  sessionId
}) => {
  const [poseData, setPoseData] = useState<PoseDataPoint[]>([]);
  const [currentPose, setCurrentPose] = useState<PoseDataPoint | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load pre-processed pose data when sessionId is provided
  useEffect(() => {
    console.log('SkeletalOverlay useEffect triggered with sessionId:', sessionId);
    if (sessionId) {
      loadPoseData();
    } else {
      console.log('No sessionId provided, skipping pose data loading');
    }
  }, [sessionId]);



  // Update current pose based on video time
  useEffect(() => {
    if (poseData.length > 0 && videoRef.current) {
      const currentVideoTime = currentTime;

      // Find the closest pose data point to current video time
      const closestPose = poseData.reduce((closest, pose) => {
        const currentDiff = Math.abs(pose.timestamp_seconds - currentVideoTime);
        const closestDiff = Math.abs(closest.timestamp_seconds - currentVideoTime);
        return currentDiff < closestDiff ? pose : closest;
      });

      setCurrentPose(closestPose);
    }
  }, [currentTime, poseData]);

  const loadPoseData = async () => {
    if (!sessionId) {
      console.log('No sessionId provided to loadPoseData');
      return;
    }

    console.log('Loading pose data for sessionId:', sessionId);
    setIsLoading(true);
    try {
      // Import supabase here to avoid module resolution issues
      const { default: supabase } = await import('../utils/supabaseClient');

      console.log('Querying pose_data table for session_id:', sessionId);
      const { data, error } = await supabase
        .from('pose_data')
        .select('*')
        .eq('session_id', sessionId)
        .order('frame_number');

      if (error) {
        console.error('Error loading pose data:', error);
        setError(`Failed to load pose data: ${error.message}`);
        return;
      }

      console.log('Loaded pose data:', data);
      console.log('Number of pose data points:', data?.length || 0);

      if (!data || data.length === 0) {
        console.log('No pose data found in database for sessionId:', sessionId);
        setError('No pose data found for this session');
        setPoseData([]);
      } else {
        console.log('Sample pose data point:', data[0]);
        console.log('Pose data coordinates sample:', {
          hip_x: data[0]?.hip_x,
          hip_y: data[0]?.hip_y,
          knee_x: data[0]?.knee_x,
          knee_y: data[0]?.knee_y,
          pose_detected: data[0]?.pose_detected
        });
        setPoseData(data);
        setError(null);
      }
    } catch (err) {
      console.error('Error loading pose data:', err);
      setError(`Failed to load pose data: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // This component only displays pre-processed pose data, no real-time analysis needed

  const renderRunningSkeletonSide = () => {
    // Use the new currentPose data instead of old poseData structure
    if (!currentPose || !currentPose.pose_detected) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg">
            {isLoading ? 'Loading pose data...' : 'No pose detected'}
          </div>
        </div>
      );
    }

    // Get actual video dimensions for logging (not needed for coordinate conversion)
    const videoElement = videoRef.current;
    const videoWidth = videoElement?.videoWidth || 320;  // Fallback to 320
    const videoHeight = videoElement?.videoHeight || 568; // Fallback to 568

    console.log('Actual video dimensions:', { videoWidth, videoHeight });

    // Pose coordinates are already normalized to 0-100 range by poseAnalysis.ts
    // No additional normalization needed - use coordinates directly
    const joints = {
      hip: {
        x: currentPose.hip_x ?? 50,
        y: currentPose.hip_y ?? 50
      },
      knee: {
        x: currentPose.knee_x ?? 50,
        y: currentPose.knee_y ?? 70
      },
      ankle: {
        x: currentPose.ankle_x ?? 50,
        y: currentPose.ankle_y ?? 90
      },
      trunk: {
        x: currentPose.trunk_x ?? 50,
        y: currentPose.trunk_y ?? 30
      },
      neck: {
        x: currentPose.neck_x ?? 50,
        y: currentPose.neck_y ?? 10
      }
    };

    console.log('Raw pose data from database:', {
      hip_x: currentPose.hip_x,
      hip_y: currentPose.hip_y,
      knee_x: currentPose.knee_x,
      knee_y: currentPose.knee_y,
      ankle_x: currentPose.ankle_x,
      ankle_y: currentPose.ankle_y,
      trunk_x: currentPose.trunk_x,
      trunk_y: currentPose.trunk_y,
      neck_x: currentPose.neck_x,
      neck_y: currentPose.neck_y
    });

    console.log('Final joint coordinates for SVG (should be 0-100 range):', joints);

    // Calculate adaptive sizes based on video dimensions
    const baseSize = Math.min(videoWidth, videoHeight);
    const strokeWidth = Math.max(1, Math.round(baseSize / 200)); // Adaptive stroke width
    const circleRadius = Math.max(2, Math.round(baseSize / 150)); // Adaptive circle radius
    const fontSize = Math.max(2, Math.round(baseSize / 180)); // Adaptive font size

    return (
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        viewBox="0 0 100 100"
        preserveAspectRatio="xMidYMid meet"
        style={{
          zIndex: 50,
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%'
        }}
      >
        <defs>
          <filter id="glow">
            <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* Skeleton lines */}
        <g className="skeleton-lines">
          {/* Torso line (neck to trunk to hip) */}
          <line
            x1={joints.neck.x}
            y1={joints.neck.y}
            x2={joints.trunk.x}
            y2={joints.trunk.y}
            stroke="#0080FF"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            opacity="0.9"
          />
          <line
            x1={joints.trunk.x}
            y1={joints.trunk.y}
            x2={joints.hip.x}
            y2={joints.hip.y}
            stroke="#0080FF"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            opacity="0.9"
          />

          {/* Leg line (hip to knee to ankle) */}
          <line
            x1={joints.hip.x}
            y1={joints.hip.y}
            x2={joints.knee.x}
            y2={joints.knee.y}
            stroke="#0080FF"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            opacity="0.9"
          />
          <line
            x1={joints.knee.x}
            y1={joints.knee.y}
            x2={joints.ankle.x}
            y2={joints.ankle.y}
            stroke="#0080FF"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            opacity="0.9"
          />
        </g>

        {/* Joint markers */}
        <g className="joint-markers">
          {Object.entries(joints).map(([joint, pos]) => (
            <g key={joint}>
              <circle
                cx={pos.x}
                cy={pos.y}
                r={circleRadius}
                fill={joint === 'hip' ? '#FF0000' : joint === 'knee' ? '#FFFF00' : joint === 'ankle' ? '#00FF00' : '#0080FF'}
                stroke="white"
                strokeWidth={Math.max(0.5, strokeWidth * 0.5)}
                opacity="0.9"
              />

            </g>
          ))}
        </g>

        {/* Angle labels */}
        <g className="angle-labels">
          <g>
            <rect
              x={scaledJoints.hip.x - 10}
              y={scaledJoints.hip.y - 6}
              width="20"
              height="8"
              rx="2"
              fill="rgba(255,140,0,0.9)"
              stroke="white"
              strokeWidth={Math.max(0.3, strokeWidth * 0.3)}
            />
            <text
              x={scaledJoints.hip.x}
              y={scaledJoints.hip.y - 1}
              textAnchor="middle"
              fill="white"
              fontSize={fontSize}
              fontWeight="bold"
            >
              {Math.round(currentPose.hip_angle)}°
            </text>
          </g>

          <g>
            <rect
              x={scaledJoints.knee.x - 10}
              y={scaledJoints.knee.y - 6}
              width="20"
              height="8"
              rx="2"
              fill="rgba(255,140,0,0.9)"
              stroke="white"
              strokeWidth={Math.max(0.3, strokeWidth * 0.3)}
            />
            <text
              x={scaledJoints.knee.x}
              y={scaledJoints.knee.y - 1}
              textAnchor="middle"
              fill="white"
              fontSize={fontSize}
              fontWeight="bold"
            >
              {Math.round(currentPose.knee_angle)}°
            </text>
          </g>
        </g>

        {/* Performance metrics overlay */}
        <g className="metrics-overlay">
          <rect
            x="2"
            y="2"
            width="30"
            height="15"
            rx="2"
            fill="rgba(0,0,0,0.7)"
          />
          <text x="4" y="6" fill="white" fontSize="3" fontWeight="500">
            Hip: {Math.round(currentPose.hip_angle)}°
          </text>
          <text x="4" y="10" fill="white" fontSize="3" fontWeight="500">
            Knee: {Math.round(currentPose.knee_angle)}°
          </text>
          <text x="4" y="14" fill="white" fontSize="3" fontWeight="500">
            Confidence: {Math.round(currentPose.detection_confidence * 100)}%
          </text>
        </g>


      </svg>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 20 }}>
      {error && (
        <div className="absolute top-2 left-2 bg-red-500 text-white px-3 py-2 rounded text-sm z-30">
          {error}
        </div>
      )}





      {activity === 'running' && view === 'side' && renderRunningSkeletonSide()}
    </div>
  );
};

export default SkeletalOverlay;