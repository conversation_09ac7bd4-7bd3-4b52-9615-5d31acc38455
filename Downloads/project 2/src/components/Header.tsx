import React from 'react';
import { ActivityType } from '../types';
import { Activity, BarChart3, Rewind, Database, Cpu } from 'lucide-react';

type ViewType = 'upload' | 'analysis' | 'preprocessing' | 'data-management';

interface HeaderProps {
  activity: ActivityType;
  onActivityChange: (activity: ActivityType) => void;
  onReset: () => void;
  currentView: ViewType;
  onViewChange?: (view: ViewType) => void;
}

const Header: React.FC<HeaderProps> = ({
  activity,
  onActivityChange,
  onReset,
  currentView,
  onViewChange
}) => {
  const getViewTitle = () => {
    switch (currentView) {
      case 'upload': return 'Video Analysis Tool';
      case 'analysis': return 'Analysis Results';
      case 'preprocessing': return 'Video Pre-Processing';
      case 'data-management': return 'Data Management';
      default: return 'Video Analysis Tool';
    }
  };

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Activity className="text-primary w-6 h-6" />
            <h1 className="text-xl font-semibold">{getViewTitle()}</h1>
          </div>

          {/* Navigation */}
          {onViewChange && (
            <nav className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => onViewChange('upload')}
                className={`px-3 py-1 rounded-md text-sm transition ${
                  currentView === 'upload' || currentView === 'analysis'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Activity className="w-4 h-4 inline mr-1" />
                Analysis
              </button>
              <button
                onClick={() => onViewChange('preprocessing')}
                className={`px-3 py-1 rounded-md text-sm transition ${
                  currentView === 'preprocessing'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Cpu className="w-4 h-4 inline mr-1" />
                Pre-Process
              </button>
              <button
                onClick={() => onViewChange('data-management')}
                className={`px-3 py-1 rounded-md text-sm transition ${
                  currentView === 'data-management'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Database className="w-4 h-4 inline mr-1" />
                Data
              </button>
            </nav>
          )}

          <div className="flex items-center space-x-4">
            {currentView === 'analysis' && (
              <button
                onClick={onReset}
                className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-primary transition"
              >
                <Rewind className="w-4 h-4" />
                <span>New Analysis</span>
              </button>
            )}

            {currentView === 'upload' && (
              <div className="flex items-center space-x-2">
                <label htmlFor="activity" className="text-sm text-gray-600 dark:text-gray-300">
                  Activity:
                </label>
                <select
                  id="activity"
                  value={activity}
                  onChange={(e) => onActivityChange(e.target.value as ActivityType)}
                  className="bg-gray-100 dark:bg-gray-700 border-none rounded-md py-1 px-3 text-sm focus:ring-2 focus:ring-primary"
                >
                  <option value="running">Running</option>
                  <option value="cycling">Cycling</option>
                </select>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;