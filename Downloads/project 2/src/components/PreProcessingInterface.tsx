import React, { useState, useCallback } from 'react';
import { Upload, Play, CheckCircle, AlertCircle, Clock, Database, BarChart3 } from 'lucide-react';
import { ActivityType, VideoType } from '../types';
import { VideoPreProcessor, PreProcessingProgress, PreProcessingResult } from '../utils/videoPreProcessor';

interface PreProcessingInterfaceProps {
  onComplete?: (result: PreProcessingResult) => void;
}

interface ProcessingJob {
  id: string;
  file: File;
  activityType: ActivityType;
  viewType: VideoType;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress?: PreProcessingProgress;
  result?: PreProcessingResult;
  error?: string;
}

const PreProcessingInterface: React.FC<PreProcessingInterfaceProps> = ({ onComplete }) => {
  const [jobs, setJobs] = useState<ProcessingJob[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<ActivityType>('running');
  const [selectedViewType, setSelectedViewType] = useState<VideoType>('side');

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newJobs: ProcessingJob[] = Array.from(files).map(file => ({
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      file,
      activityType: selectedActivity,
      viewType: selectedViewType,
      status: 'queued'
    }));

    setJobs(prev => [...prev, ...newJobs]);
  }, [selectedActivity, selectedViewType]);

  const processJob = async (job: ProcessingJob) => {
    const processor = new VideoPreProcessor((progress) => {
      setJobs(prev => prev.map(j => 
        j.id === job.id 
          ? { ...j, progress, status: 'processing' }
          : j
      ));
    });

    try {
      const result = await processor.preProcessVideo(
        job.file,
        job.activityType,
        job.viewType
      );

      setJobs(prev => prev.map(j => 
        j.id === job.id 
          ? { ...j, status: 'completed', result }
          : j
      ));

      if (onComplete) {
        onComplete(result);
      }
    } catch (error) {
      console.error('Processing failed:', error);
      setJobs(prev => prev.map(j => 
        j.id === job.id 
          ? { ...j, status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
          : j
      ));
    }
  };

  const startProcessing = async () => {
    setIsProcessing(true);
    
    const queuedJobs = jobs.filter(job => job.status === 'queued');
    
    for (const job of queuedJobs) {
      await processJob(job);
    }
    
    setIsProcessing(false);
  };

  const clearCompleted = () => {
    setJobs(prev => prev.filter(job => job.status !== 'completed'));
  };

  const removeJob = (jobId: string) => {
    setJobs(prev => prev.filter(job => job.id !== jobId));
  };

  const getStatusIcon = (status: ProcessingJob['status']) => {
    switch (status) {
      case 'queued':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'processing':
        return <Play className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Database className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">Video Pre-Processing</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Process videos offline to extract detailed pose analysis data for data science research. 
          This creates a comprehensive dataset with frame-by-frame pose data, metrics, and recommendations.
        </p>
      </div>

      {/* Configuration */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Analysis Configuration</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Activity Type
            </label>
            <select
              value={selectedActivity}
              onChange={(e) => setSelectedActivity(e.target.value as ActivityType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="running">Running Analysis</option>
              <option value="cycling">Cycling Analysis</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Camera View
            </label>
            <select
              value={selectedViewType}
              onChange={(e) => setSelectedViewType(e.target.value as VideoType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="side">Side View</option>
              <option value="rear">Rear View</option>
            </select>
          </div>
        </div>
      </div>

      {/* File Upload */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Upload Videos</h2>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-lg font-medium text-gray-900 mb-2">
            Drop video files here or click to browse
          </p>
          <p className="text-sm text-gray-500 mb-4">
            Supports MP4, MOV, AVI, WebM • Portrait videos recommended
          </p>
          <input
            type="file"
            multiple
            accept=".mp4,.mov,.avi,.webm"
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
            id="video-upload"
          />
          <label
            htmlFor="video-upload"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
          >
            <Upload className="w-4 h-4 mr-2" />
            Select Videos
          </label>
        </div>
      </div>

      {/* Processing Queue */}
      {jobs.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Processing Queue</h2>
            <div className="space-x-2">
              <button
                onClick={startProcessing}
                disabled={isProcessing || jobs.filter(j => j.status === 'queued').length === 0}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isProcessing ? 'Processing...' : 'Start Processing'}
              </button>
              <button
                onClick={clearCompleted}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Clear Completed
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {jobs.map((job) => (
              <div key={job.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(job.status)}
                    <div>
                      <p className="font-medium text-gray-900">{job.file.name}</p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(job.file.size)} • {job.activityType} • {job.viewType} view
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    {job.status === 'processing' && job.progress && (
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {Math.round(job.progress.progress)}%
                        </p>
                        <p className="text-xs text-gray-500">
                          Frame {job.progress.currentFrame + 1} of {job.progress.totalFrames}
                        </p>
                      </div>
                    )}
                    
                    {job.status === 'completed' && job.result && (
                      <div className="text-right">
                        <p className="text-sm font-medium text-green-600">
                          {job.result.successfulDetections} detections
                        </p>
                        <p className="text-xs text-gray-500">
                          {Math.round(job.result.detectionRate)}% success rate
                        </p>
                      </div>
                    )}
                    
                    {job.status === 'failed' && (
                      <div className="text-right">
                        <p className="text-sm font-medium text-red-600">Failed</p>
                        <p className="text-xs text-gray-500">{job.error}</p>
                      </div>
                    )}
                    
                    <button
                      onClick={() => removeJob(job.id)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      ×
                    </button>
                  </div>
                </div>
                
                {job.status === 'processing' && job.progress && (
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${job.progress.progress}%` }}
                      />
                    </div>
                  </div>
                )}
                
                {job.status === 'completed' && job.result && (
                  <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Processing Time</p>
                      <p className="font-medium">{formatDuration(job.result.processingTimeSeconds)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Avg Posture Score</p>
                      <p className="font-medium">{Math.round(job.result.averageMetrics.postureScore)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Avg Knee Angle</p>
                      <p className="font-medium">{Math.round(job.result.averageMetrics.kneeAngle)}°</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Recommendations</p>
                      <p className="font-medium">{job.result.recommendations.length}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Data Science Info */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <BarChart3 className="w-6 h-6 text-blue-600 mt-1" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Data Science Ready Output
            </h3>
            <p className="text-gray-700 mb-3">
              Pre-processing creates comprehensive datasets stored in Supabase with:
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Frame-by-frame pose data with joint angles and positions</li>
              <li>• Performance metrics and biomechanical analysis</li>
              <li>• Equipment recommendations based on movement patterns</li>
              <li>• Structured data ready for machine learning models</li>
              <li>• Export capabilities for external analysis tools</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreProcessingInterface;
