import React from 'react';
import { motion } from 'framer-motion';

interface MetricCardProps {
  metric: {
    id: string;
    score: number;
    label: string;
    value: string;
    notes: string;
  };
}

const MetricCard: React.FC<MetricCardProps> = ({ metric }) => {
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 80) return 'bg-primary';
    if (score >= 70) return 'bg-yellow-500';
    return 'bg-accent';
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 * Math.random() }}
      className="metric-card p-3 bg-gray-50 dark:bg-gray-700"
    >
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-medium text-gray-800 dark:text-gray-100">
          {metric.label}
        </h4>
        <span className={`px-2 py-0.5 rounded-full text-xs font-medium text-white ${getScoreColor(metric.score)}`}>
          {metric.score}%
        </span>
      </div>
      
      <div className="flex items-center space-x-2 mb-2">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {metric.value}
        </div>
      </div>
      
      <div className="progress-bar">
        <div 
          className={`progress-bar-fill ${metric.score >= 80 ? 'good' : 'warning'}`}
          style={{ width: `${metric.score}%` }}
        />
      </div>
      
      <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
        {metric.notes}
      </p>
    </motion.div>
  );
};

export default MetricCard;