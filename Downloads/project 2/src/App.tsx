import { useState } from 'react';
import Header from './components/Header';
import VideoUploader from './components/VideoUploader';
import AnalysisDisplay from './components/AnalysisDisplay';
import PreProcessingInterface from './components/PreProcessingInterface';
import DataManagementDashboard from './components/DataManagementDashboard';
import { ActivityType, VideoType, AnalysisResults } from './types';
import { createClient } from '@supabase/supabase-js';


// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;
const supabase = createClient(supabaseUrl, supabaseKey);

function App() {
  const [selectedActivity, setSelectedActivity] = useState<ActivityType>('running');
  const [uploadedVideos, setUploadedVideos] = useState<Record<string, string>>({});
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [currentView, setCurrentView] = useState<'upload' | 'analysis' | 'preprocessing' | 'data-management'>('upload');

  const handleVideoUpload = async (videoType: VideoType, file: File | null) => {
    try {
      if (!file) {
        // If file is null, remove the video
        setUploadedVideos(prev => {
          const newVideos = { ...prev };
          delete newVideos[videoType];
          return newVideos;
        });
        return null;
      }

      // Get file extension from the file type or name
      let fileExtension = '.mp4'; // Default extension
      let contentType = file.type || 'video/mp4';

      // Special handling for iPhone videos
      const isIPhoneVideo = file.name.toLowerCase().endsWith('.mov') ||
                           file.type === 'video/quicktime' ||
                           (file.name.includes('IMG_') && file.name.includes('.MOV'));

      if (isIPhoneVideo) {
        console.log('Detected iPhone video, using special handling');
        fileExtension = '.mov';
        contentType = 'video/quicktime';
      } else if (file.type) {
        // Extract extension from MIME type
        if (file.type === 'video/mp4') fileExtension = '.mp4';
        else if (file.type === 'video/quicktime') fileExtension = '.mov';
        else if (file.type === 'video/x-msvideo') fileExtension = '.avi';
        else if (file.type === 'video/webm') fileExtension = '.webm';
        // Fallback to extracting from filename if available
        else if (file.name && file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.substring(file.name.lastIndexOf('.'));
        }
      }

      const timestamp = new Date().getTime();
      // Use the correct bucket folder structure
      const folderPath = `${selectedActivity}-${videoType}`;
      const filePath = `${folderPath}/video_${timestamp}${fileExtension}`;

      console.log(`Uploading video with type: ${file.type}, extension: ${fileExtension}, contentType: ${contentType}`);

      const { error } = await supabase.storage
        .from('videos')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          contentType: contentType // Use our determined content type
        });

      if (error) {
        console.error('Supabase upload error:', error);
        throw error;
      }

      // Get the public URL for the uploaded video
      const { data: { publicUrl } } = supabase.storage
        .from('videos')
        .getPublicUrl(filePath);

      console.log(`Video uploaded successfully. Public URL: ${publicUrl}`);

      setUploadedVideos(prev => ({
        ...prev,
        [videoType]: publicUrl
      }));

      // Video processing will happen when user clicks "Start Analysis"
      // No need to process individual videos during upload

      return publicUrl;
    } catch (error) {
      console.error('Error uploading video:', error);
      return null;
    }
  };



  const startAnalysis = async () => {
    console.log('Starting analysis with videos:', uploadedVideos);

    if (!uploadedVideos.side) {
      alert('Please upload a side view video before analyzing');
      return;
    }

    setIsAnalyzing(true);

    try {
      console.log('Starting real-time video analysis with pose detection...');

      // Import the pre-processor
      const { VideoPreProcessor } = await import('./utils/videoPreProcessor');

      // Create processor with progress callback
      const processor = new VideoPreProcessor((progress) => {
        console.log('Processing progress:', progress);
        // You could add a progress state here if needed
      });

      // Process the already-uploaded video directly from the URL
      console.log('Processing side view video with real pose detection...');
      const processingResult = await processor.processUploadedVideo(
        uploadedVideos.side,
        selectedActivity,
        'side'
      );

      console.log('Video processing completed successfully:', processingResult);

      // Convert processing result to analysis results format
      const analysisResults = {
        overallScore: Math.round(processingResult.averageMetrics.postureScore),
        metrics: {
          stride: {
            score: Math.round(processingResult.averageMetrics.postureScore),
            label: 'Stride Length',
            value: `${processingResult.averageMetrics.strideLength.toFixed(2)}m`,
            notes: processingResult.averageMetrics.strideLength > 1.3 ? 'Good stride length' : 'Consider adjusting stride length'
          },
          posture: {
            score: Math.round(processingResult.averageMetrics.postureScore),
            label: 'Posture',
            value: processingResult.averageMetrics.postureScore > 80 ? 'Good' : 'Needs improvement',
            notes: processingResult.averageMetrics.postureScore > 80 ? 'Good upright posture' : 'Focus on maintaining upright posture'
          },
          footStrike: {
            score: 85, // Could be calculated from foot strike data
            label: 'Foot Strike',
            value: 'Midfoot',
            notes: 'Good landing pattern detected'
          },
          kneeAngle: {
            score: Math.round((processingResult.averageMetrics.kneeAngle / 140) * 100),
            label: 'Knee Flexion',
            value: `${Math.round(processingResult.averageMetrics.kneeAngle)}°`,
            notes: 'Good knee drive during stance'
          },
          pronation: {
            score: 78,
            label: 'Pronation',
            value: 'Normal',
            notes: 'Good foot landing mechanics'
          }
        },
        jointAngles: {
          knee: Math.round(processingResult.averageMetrics.kneeAngle),
          ankle: Math.round(processingResult.averageMetrics.ankleAngle),
          hip: Math.round(processingResult.averageMetrics.hipAngle),
        },
        // Store the session ID for retrieving pose data during playback
        sessionId: processingResult.sessionId,
        videoId: processingResult.videoId
      };

      console.log('Generated analysis results with session data:', analysisResults);
      console.log('SessionID being set:', analysisResults.sessionId);

      // Set results and change view
      setAnalysisResults(analysisResults);
      setCurrentView('analysis');
      setIsAnalyzing(false);

    } catch (error) {
      console.error('Error during video analysis:', error);
      setIsAnalyzing(false);

      // Show user-friendly error message
      alert(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again or check your video format.`);
    }
  };



  const resetAnalysis = () => {
    setUploadedVideos({});
    setAnalysisResults(null);
    setCurrentView('upload');
  };

  const handleViewChange = (view: 'upload' | 'analysis' | 'preprocessing' | 'data-management') => {
    setCurrentView(view);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <Header
        activity={selectedActivity}
        onActivityChange={setSelectedActivity}
        onReset={resetAnalysis}
        currentView={currentView}
        onViewChange={handleViewChange}
      />

      <main className="flex-1 container mx-auto px-4 py-8">
        {currentView === 'upload' ? (
          <VideoUploader
            activity={selectedActivity}
            onVideoUpload={handleVideoUpload}
            uploadedVideos={uploadedVideos}
            onStartAnalysis={startAnalysis}
            isAnalyzing={isAnalyzing}
          />
        ) : currentView === 'analysis' ? (
          <AnalysisDisplay
            activity={selectedActivity}
            results={analysisResults}
            uploadedVideos={uploadedVideos}
          />
        ) : currentView === 'preprocessing' ? (
          <PreProcessingInterface />
        ) : currentView === 'data-management' ? (
          <DataManagementDashboard />
        ) : null}
      </main>
    </div>
  );
}

export default App;