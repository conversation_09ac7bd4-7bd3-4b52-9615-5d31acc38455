import { createClient } from '@supabase/supabase-js';
import { initializePoseDetection, analyzePose, PoseAnalysis } from './poseAnalysis';
import { ActivityType, VideoType } from '../types';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;
const supabase = createClient(supabaseUrl, supabaseKey);

export interface VideoMetadata {
  filename: string;
  filePath: string;
  fileSize: number;
  duration: number;
  width: number;
  height: number;
  fps: number;
  activityType: ActivityType;
  viewType: VideoType;
}

export interface PreProcessingProgress {
  videoId: string;
  sessionId: string;
  totalFrames: number;
  processedFrames: number;
  successfulDetections: number;
  currentFrame: number;
  progress: number; // 0-100
  status: 'initializing' | 'processing' | 'completed' | 'failed';
  error?: string;
}

export interface PreProcessingResult {
  videoId: string;
  sessionId: string;
  totalFrames: number;
  successfulDetections: number;
  detectionRate: number;
  averageMetrics: {
    hipAngle: number;
    kneeAngle: number;
    ankleAngle: number;
    trunkAngle: number;
    neckAngle: number;
    strideLength: number;
    postureScore: number;
  };
  processingTimeSeconds: number;
  recommendations: Array<{
    category: string;
    priority: string;
    title: string;
    description: string;
    confidence: number;
  }>;
}

export class VideoPreProcessor {
  private video: HTMLVideoElement | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private onProgress?: (progress: PreProcessingProgress) => void;

  constructor(onProgress?: (progress: PreProcessingProgress) => void) {
    this.onProgress = onProgress;
  }

  async preProcessVideo(
    videoFile: File,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<PreProcessingResult> {
    const startTime = Date.now();

    try {
      // 1. Upload video and create database record
      const videoMetadata = await this.uploadAndCreateVideoRecord(videoFile, activityType, viewType);

      // 2. Initialize pose detection
      await initializePoseDetection();

      // 3. Create analysis session
      const sessionId = await this.createAnalysisSession(videoMetadata.videoId, videoMetadata);

      // 4. Process video frame by frame
      const analysisResult = await this.processVideoFrames(videoMetadata, sessionId);

      // 5. Calculate performance metrics and recommendations
      await this.calculatePerformanceMetrics(sessionId, analysisResult);
      const recommendations = await this.generateRecommendations(sessionId, analysisResult);

      // 6. Update session with final results
      await this.updateAnalysisSession(sessionId, analysisResult);

      // 7. Mark video as completed
      await this.updateVideoStatus(videoMetadata.videoId, 'completed');

      const processingTimeSeconds = (Date.now() - startTime) / 1000;

      return {
        videoId: videoMetadata.videoId,
        sessionId,
        totalFrames: analysisResult.totalFrames,
        successfulDetections: analysisResult.successfulDetections,
        detectionRate: analysisResult.detectionRate,
        averageMetrics: analysisResult.averageMetrics,
        processingTimeSeconds,
        recommendations
      };

    } catch (error) {
      console.error('Pre-processing failed:', error);
      throw error;
    } finally {
      this.cleanup();
    }
  }

  async processUploadedVideo(
    videoUrl: string,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<PreProcessingResult> {
    const startTime = Date.now();

    try {
      // 1. Create a minimal video record for the already-uploaded video
      const videoMetadata = await this.createVideoRecordFromUrl(videoUrl, activityType, viewType);

      // 2. Initialize pose detection
      await initializePoseDetection();

      // 3. Create analysis session
      const sessionId = await this.createAnalysisSession(videoMetadata.videoId, videoMetadata);

      // 4. Process video frame by frame directly from URL
      const analysisResult = await this.processVideoFramesFromUrl(videoUrl, sessionId);

      // 5. Calculate performance metrics and recommendations
      await this.calculatePerformanceMetrics(sessionId, analysisResult);
      const recommendations = await this.generateRecommendations(sessionId, analysisResult);

      // 6. Update session with final results
      await this.updateAnalysisSession(sessionId, analysisResult);

      // 7. Mark video as completed
      await this.updateVideoStatus(videoMetadata.videoId, 'completed');

      const processingTimeSeconds = (Date.now() - startTime) / 1000;

      return {
        videoId: videoMetadata.videoId,
        sessionId,
        totalFrames: analysisResult.totalFrames,
        successfulDetections: analysisResult.successfulDetections,
        detectionRate: analysisResult.detectionRate,
        averageMetrics: analysisResult.averageMetrics,
        processingTimeSeconds,
        recommendations
      };

    } catch (error) {
      console.error('Processing uploaded video failed:', error);
      throw error;
    } finally {
      this.cleanup();
    }
  }

  private async uploadAndCreateVideoRecord(
    file: File,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<VideoMetadata & { videoId: string }> {
    // Upload to Supabase storage
    const timestamp = new Date().getTime();
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'mp4';
    const folderPath = `${activityType}-${viewType}`;
    const filePath = `${folderPath}/video_${timestamp}.${fileExtension}`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('videos')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) throw uploadError;

    // Get video metadata
    const videoMetadata = await this.extractVideoMetadata(file);

    // Create database record
    const { data: videoRecord, error: dbError } = await supabase
      .from('pose_videos')
      .insert({
        filename: file.name,
        file_path: filePath,
        file_size: file.size,
        duration: videoMetadata.duration,
        width: videoMetadata.width,
        height: videoMetadata.height,
        fps: videoMetadata.fps,
        activity_type: activityType,
        view_type: viewType,
        processing_status: 'processing',
        processing_started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (dbError) throw dbError;

    return {
      videoId: videoRecord.id,
      filename: file.name,
      filePath,
      fileSize: file.size,
      duration: videoMetadata.duration,
      width: videoMetadata.width,
      height: videoMetadata.height,
      fps: videoMetadata.fps,
      activityType,
      viewType
    };
  }

  private async extractVideoMetadata(file: File): Promise<Omit<VideoMetadata, 'filename' | 'filePath' | 'fileSize' | 'activityType' | 'viewType'>> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight,
          fps: 30 // Default, could be extracted more accurately with additional libraries
        });
      };

      video.onerror = () => reject(new Error('Failed to load video metadata'));
      video.src = URL.createObjectURL(file);
    });
  }

  private async createVideoRecordFromUrl(
    videoUrl: string,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<VideoMetadata & { videoId: string }> {
    // Extract metadata from the video URL
    const videoMetadata = await this.extractVideoMetadataFromUrl(videoUrl);

    // Create database record
    const { data: videoRecord, error: dbError } = await supabase
      .from('pose_videos')
      .insert({
        filename: `uploaded_${activityType}_${viewType}_video.mp4`,
        file_path: videoUrl, // Store the full URL as the file path
        file_size: 0, // Unknown for already uploaded videos
        duration: videoMetadata.duration,
        width: videoMetadata.width,
        height: videoMetadata.height,
        fps: videoMetadata.fps,
        activity_type: activityType,
        view_type: viewType,
        processing_status: 'processing',
        processing_started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (dbError) throw dbError;

    return {
      videoId: videoRecord.id,
      filename: `uploaded_${activityType}_${viewType}_video.mp4`,
      filePath: videoUrl,
      fileSize: 0,
      duration: videoMetadata.duration,
      width: videoMetadata.width,
      height: videoMetadata.height,
      fps: videoMetadata.fps,
      activityType,
      viewType
    };
  }

  private async extractVideoMetadataFromUrl(videoUrl: string): Promise<Omit<VideoMetadata, 'filename' | 'filePath' | 'fileSize' | 'activityType' | 'viewType'>> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';
      video.crossOrigin = 'anonymous';

      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight,
          fps: 30 // Default, could be extracted more accurately with additional libraries
        });
      };

      video.onerror = () => reject(new Error('Failed to load video metadata from URL'));
      video.src = videoUrl;
    });
  }

  private async createAnalysisSession(videoId: string, metadata: VideoMetadata): Promise<string> {
    const { data, error } = await supabase
      .from('pose_sessions')
      .insert({
        video_id: videoId,
        model_type: 'BlazePose',
        analysis_fps: Math.min(metadata.fps, 10) // Analyze at most 10 FPS
      })
      .select()
      .single();

    if (error) throw error;
    return data.id;
  }

  private async processVideoFrames(
    metadata: VideoMetadata,
    sessionId: string
  ): Promise<{
    totalFrames: number;
    successfulDetections: number;
    detectionRate: number;
    averageMetrics: any;
    frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }>;
  }> {
    // Create video element and canvas for processing
    this.video = document.createElement('video');
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');

    if (!this.ctx) throw new Error('Failed to get canvas context');

    // Load video
    await this.loadVideo(metadata.filePath);

    this.canvas.width = this.video.videoWidth;
    this.canvas.height = this.video.videoHeight;

    const analysisFps = Math.min(metadata.fps, 10);
    const frameInterval = metadata.fps / analysisFps;
    const totalFrames = Math.floor(metadata.duration * analysisFps);

    const frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }> = [];
    let successfulDetections = 0;

    const metrics = {
      hipAngles: [] as number[],
      kneeAngles: [] as number[],
      ankleAngles: [] as number[],
      trunkAngles: [] as number[],
      neckAngles: [] as number[],
      strideLengths: [] as number[],
      postureScores: [] as number[]
    };

    for (let frameNumber = 0; frameNumber < totalFrames; frameNumber++) {
      const timestamp = (frameNumber * frameInterval) / metadata.fps;

      // Seek to frame
      this.video.currentTime = timestamp;
      await this.waitForSeek();

      // Draw frame to canvas
      this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

      try {
        // Analyze pose
        const analysis = await analyzePose(this.canvas);
        frameData.push({ frameNumber, timestamp, analysis });

        // Store frame data in database
        await this.storePoseData(sessionId, frameNumber, timestamp, analysis);

        // Collect metrics
        metrics.hipAngles.push(analysis.angles.hip.angle);
        metrics.kneeAngles.push(analysis.angles.knee.angle);
        metrics.ankleAngles.push(analysis.angles.ankle.angle);
        metrics.trunkAngles.push(analysis.angles.trunk.angle);
        metrics.neckAngles.push(analysis.angles.neck.angle);
        metrics.strideLengths.push(analysis.metrics.strideLength);
        metrics.postureScores.push(analysis.metrics.postureScore);

        successfulDetections++;
      } catch (error) {
        console.warn(`Failed to analyze frame ${frameNumber}:`, error);
        frameData.push({ frameNumber, timestamp, analysis: null });

        // Store failed detection
        await this.storePoseData(sessionId, frameNumber, timestamp, null);
      }

      // Report progress
      if (this.onProgress) {
        this.onProgress({
          videoId: metadata.videoId,
          sessionId,
          totalFrames,
          processedFrames: frameNumber + 1,
          successfulDetections,
          currentFrame: frameNumber,
          progress: ((frameNumber + 1) / totalFrames) * 100,
          status: 'processing'
        });
      }
    }

    const averageMetrics = {
      hipAngle: this.average(metrics.hipAngles),
      kneeAngle: this.average(metrics.kneeAngles),
      ankleAngle: this.average(metrics.ankleAngles),
      trunkAngle: this.average(metrics.trunkAngles),
      neckAngle: this.average(metrics.neckAngles),
      strideLength: this.average(metrics.strideLengths),
      postureScore: this.average(metrics.postureScores)
    };

    return {
      totalFrames,
      successfulDetections,
      detectionRate: (successfulDetections / totalFrames) * 100,
      averageMetrics,
      frameData
    };
  }

  private async processVideoFramesFromUrl(
    videoUrl: string,
    sessionId: string
  ): Promise<{
    totalFrames: number;
    successfulDetections: number;
    detectionRate: number;
    averageMetrics: any;
    frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }>;
  }> {
    // Create video element and canvas for processing
    this.video = document.createElement('video');
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');

    if (!this.ctx) throw new Error('Failed to get canvas context');

    // Load video directly from URL
    await this.loadVideoFromUrl(videoUrl);

    this.canvas.width = this.video.videoWidth;
    this.canvas.height = this.video.videoHeight;

    const fps = 30; // Default FPS
    const analysisFps = Math.min(fps, 10);
    const frameInterval = fps / analysisFps;
    const totalFrames = Math.floor(this.video.duration * analysisFps);

    const frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }> = [];
    let successfulDetections = 0;

    const metrics = {
      hipAngles: [] as number[],
      kneeAngles: [] as number[],
      ankleAngles: [] as number[],
      trunkAngles: [] as number[],
      neckAngles: [] as number[],
      strideLengths: [] as number[],
      postureScores: [] as number[]
    };

    for (let frameNumber = 0; frameNumber < totalFrames; frameNumber++) {
      const timestamp = (frameNumber * frameInterval) / fps;

      // Seek to frame
      this.video.currentTime = timestamp;
      await this.waitForSeek();

      // Draw frame to canvas
      this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

      try {
        // Analyze pose
        const analysis = await analyzePose(this.canvas);
        frameData.push({ frameNumber, timestamp, analysis });

        // Store frame data in database
        await this.storePoseData(sessionId, frameNumber, timestamp, analysis);

        // Collect metrics
        metrics.hipAngles.push(analysis.angles.hip.angle);
        metrics.kneeAngles.push(analysis.angles.knee.angle);
        metrics.ankleAngles.push(analysis.angles.ankle.angle);
        metrics.trunkAngles.push(analysis.angles.trunk.angle);
        metrics.neckAngles.push(analysis.angles.neck.angle);
        metrics.strideLengths.push(analysis.metrics.strideLength);
        metrics.postureScores.push(analysis.metrics.postureScore);

        successfulDetections++;
      } catch (error) {
        console.warn(`Failed to analyze frame ${frameNumber}:`, error);
        frameData.push({ frameNumber, timestamp, analysis: null });

        // Store failed detection
        await this.storePoseData(sessionId, frameNumber, timestamp, null);
      }

      // Report progress
      if (this.onProgress) {
        this.onProgress({
          videoId: 'url-based-video',
          sessionId,
          totalFrames,
          processedFrames: frameNumber + 1,
          successfulDetections,
          currentFrame: frameNumber,
          progress: ((frameNumber + 1) / totalFrames) * 100,
          status: 'processing'
        });
      }
    }

    const averageMetrics = {
      hipAngle: this.average(metrics.hipAngles),
      kneeAngle: this.average(metrics.kneeAngles),
      ankleAngle: this.average(metrics.ankleAngles),
      trunkAngle: this.average(metrics.trunkAngles),
      neckAngle: this.average(metrics.neckAngles),
      strideLength: this.average(metrics.strideLengths),
      postureScore: this.average(metrics.postureScores)
    };

    return {
      totalFrames,
      successfulDetections,
      detectionRate: (successfulDetections / totalFrames) * 100,
      averageMetrics,
      frameData
    };
  }

  private async loadVideo(filePath: string): Promise<void> {
    if (!this.video) throw new Error('Video element not initialized');

    const { data } = supabase.storage.from('videos').getPublicUrl(filePath);

    return new Promise((resolve, reject) => {
      this.video!.onloadeddata = () => resolve();
      this.video!.onerror = () => reject(new Error('Failed to load video'));
      this.video!.src = data.publicUrl;
    });
  }

  private async loadVideoFromUrl(videoUrl: string): Promise<void> {
    if (!this.video) throw new Error('Video element not initialized');

    return new Promise((resolve, reject) => {
      this.video!.onloadeddata = () => resolve();
      this.video!.onerror = () => reject(new Error('Failed to load video from URL'));
      this.video!.crossOrigin = 'anonymous';
      this.video!.src = videoUrl;
    });
  }

  private async waitForSeek(): Promise<void> {
    if (!this.video) return;

    return new Promise((resolve) => {
      const onSeeked = () => {
        this.video!.removeEventListener('seeked', onSeeked);
        resolve();
      };
      this.video.addEventListener('seeked', onSeeked);
    });
  }

  private async storePoseData(
    sessionId: string,
    frameNumber: number,
    timestamp: number,
    analysis: PoseAnalysis | null
  ): Promise<void> {
    const poseData = {
      session_id: sessionId,
      frame_number: frameNumber,
      timestamp_seconds: timestamp,
      pose_detected: analysis !== null,
      detection_confidence: analysis ? 0.8 : 0, // Could be extracted from actual detection
      hip_angle: analysis?.angles.hip.angle,
      knee_angle: analysis?.angles.knee.angle,
      ankle_angle: analysis?.angles.ankle.angle,
      trunk_angle: analysis?.angles.trunk.angle,
      neck_angle: analysis?.angles.neck.angle,
      hip_x: analysis?.angles.hip.position.x,
      hip_y: analysis?.angles.hip.position.y,
      knee_x: analysis?.angles.knee.position.x,
      knee_y: analysis?.angles.knee.position.y,
      ankle_x: analysis?.angles.ankle.position.x,
      ankle_y: analysis?.angles.ankle.position.y,
      trunk_x: analysis?.angles.trunk.position.x,
      trunk_y: analysis?.angles.trunk.position.y,
      neck_x: analysis?.angles.neck.position.x,
      neck_y: analysis?.angles.neck.position.y,
      stride_length: analysis?.metrics.strideLength,
      foot_strike_type: analysis?.metrics.footStrike,
      posture_score: analysis?.metrics.postureScore,
      raw_keypoints: analysis ? {} : null // Could store full keypoint data
    };

    // Debug log the pose data being stored
    if (analysis && frameNumber % 10 === 0) { // Log every 10th frame to avoid spam
      console.log(`Storing pose data for frame ${frameNumber}:`, {
        hip_x: poseData.hip_x,
        hip_y: poseData.hip_y,
        knee_x: poseData.knee_x,
        knee_y: poseData.knee_y,
        hip_angle: poseData.hip_angle,
        knee_angle: poseData.knee_angle
      });
    }

    const { error } = await supabase
      .from('pose_data')
      .insert(poseData);

    if (error) {
      console.error('Failed to store pose data:', error);
      // Don't throw - continue processing other frames
    }
  }

  private average(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }

  private async calculatePerformanceMetrics(sessionId: string, analysisResult: any): Promise<void> {
    // Calculate advanced metrics for data science analysis
    const metrics = [
      {
        metric_name: 'stride_consistency',
        metric_category: 'efficiency',
        value: this.calculateStrideConsistency(analysisResult.frameData),
        unit: 'coefficient_of_variation',
        score: Math.max(0, 100 - (this.calculateStrideConsistency(analysisResult.frameData) * 100))
      },
      {
        metric_name: 'knee_drive_efficiency',
        metric_category: 'power',
        value: analysisResult.averageMetrics.kneeAngle,
        unit: 'degrees',
        score: this.scoreKneeDrive(analysisResult.averageMetrics.kneeAngle)
      },
      {
        metric_name: 'posture_stability',
        metric_category: 'form',
        value: analysisResult.averageMetrics.postureScore,
        unit: 'percentage',
        score: analysisResult.averageMetrics.postureScore
      }
    ];

    for (const metric of metrics) {
      await supabase.from('pose_metrics').insert({
        session_id: sessionId,
        ...metric,
        calculation_method: 'automated_analysis',
        confidence_level: 0.85
      });
    }
  }

  private calculateStrideConsistency(frameData: any[]): number {
    const strideLengths = frameData
      .filter(frame => frame.analysis)
      .map(frame => frame.analysis.metrics.strideLength);

    if (strideLengths.length < 2) return 0;

    const mean = this.average(strideLengths);
    const variance = strideLengths.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / strideLengths.length;
    const stdDev = Math.sqrt(variance);

    return stdDev / mean; // Coefficient of variation
  }

  private scoreKneeDrive(kneeAngle: number): number {
    // Optimal knee angle for running is typically 120-140 degrees
    const optimal = 130;
    const deviation = Math.abs(kneeAngle - optimal);
    return Math.max(0, 100 - (deviation * 2));
  }

  private async generateRecommendations(sessionId: string, analysisResult: any): Promise<any[]> {
    const recommendations = [];

    // Analyze posture
    if (analysisResult.averageMetrics.postureScore < 70) {
      recommendations.push({
        category: 'form',
        priority: 'high',
        title: 'Improve Running Posture',
        description: 'Your trunk angle suggests forward lean. Focus on maintaining an upright posture with slight forward lean from ankles.',
        confidence: 0.8
      });
    }

    // Analyze foot strike
    const footStrikeData = analysisResult.frameData
      .filter(frame => frame.analysis)
      .map(frame => frame.analysis.metrics.footStrike);

    const heelStrikePercentage = (footStrikeData.filter(strike => strike === 'heel').length / footStrikeData.length) * 100;

    if (heelStrikePercentage > 70) {
      recommendations.push({
        category: 'equipment',
        priority: 'medium',
        title: 'Consider Heel-Strike Friendly Shoes',
        description: 'Your foot strike pattern shows predominantly heel striking. Consider shoes with more heel cushioning and support.',
        confidence: 0.9
      });
    }

    // Store recommendations in database
    for (const rec of recommendations) {
      await supabase.from('pose_recommendations').insert({
        session_id: sessionId,
        ...rec,
        confidence_score: rec.confidence
      });
    }

    return recommendations;
  }

  private async updateAnalysisSession(sessionId: string, analysisResult: any): Promise<void> {
    await supabase
      .from('pose_sessions')
      .update({
        total_frames_analyzed: analysisResult.totalFrames,
        successful_detections: analysisResult.successfulDetections,
        detection_rate: analysisResult.detectionRate,
        avg_hip_angle: analysisResult.averageMetrics.hipAngle,
        avg_knee_angle: analysisResult.averageMetrics.kneeAngle,
        avg_ankle_angle: analysisResult.averageMetrics.ankleAngle,
        avg_trunk_angle: analysisResult.averageMetrics.trunkAngle,
        avg_neck_angle: analysisResult.averageMetrics.neckAngle,
        avg_stride_length: analysisResult.averageMetrics.strideLength,
        avg_posture_score: analysisResult.averageMetrics.postureScore
      })
      .eq('id', sessionId);
  }

  private async updateVideoStatus(videoId: string, status: string): Promise<void> {
    await supabase
      .from('pose_videos')
      .update({
        processing_status: status,
        processing_completed_at: new Date().toISOString()
      })
      .eq('id', videoId);
  }

  private cleanup(): void {
    if (this.video) {
      this.video.src = '';
      this.video = null;
    }
    this.canvas = null;
    this.ctx = null;
  }
}
