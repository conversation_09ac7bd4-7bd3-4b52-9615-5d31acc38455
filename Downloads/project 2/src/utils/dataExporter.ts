import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;
const supabase = createClient(supabaseUrl, supabaseKey);

export interface ExportOptions {
  format: 'csv' | 'json' | 'parquet';
  includeRawKeypoints: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  activityType?: 'running' | 'cycling';
  viewType?: 'side' | 'rear';
  sessionIds?: string[];
}

export interface DatasetSummary {
  totalSessions: number;
  totalFrames: number;
  totalVideos: number;
  activityBreakdown: Record<string, number>;
  viewTypeBreakdown: Record<string, number>;
  dateRange: {
    earliest: string;
    latest: string;
  };
  averageDetectionRate: number;
}

export class DataExporter {
  async getDatasetSummary(options?: Partial<ExportOptions>): Promise<DatasetSummary> {
    // Build query filters
    let videoQuery = supabase.from('pose_videos').select('*');
    let sessionQuery = supabase.from('pose_sessions').select('*');

    if (options?.activityType) {
      videoQuery = videoQuery.eq('activity_type', options.activityType);
    }

    if (options?.viewType) {
      videoQuery = videoQuery.eq('view_type', options.viewType);
    }

    if (options?.dateRange) {
      videoQuery = videoQuery
        .gte('created_at', options.dateRange.start)
        .lte('created_at', options.dateRange.end);
    }

    const [videosResult, sessionsResult, poseDataResult] = await Promise.all([
      videoQuery,
      sessionQuery,
      supabase.from('pose_data').select('session_id').eq('pose_detected', true)
    ]);

    if (videosResult.error || sessionsResult.error || poseDataResult.error) {
      throw new Error('Failed to fetch dataset summary');
    }

    const videos = videosResult.data || [];
    const sessions = sessionsResult.data || [];
    const totalFrames = poseDataResult.data?.length || 0;

    // Calculate breakdowns
    const activityBreakdown = videos.reduce((acc, video) => {
      acc[video.activity_type] = (acc[video.activity_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const viewTypeBreakdown = videos.reduce((acc, video) => {
      acc[video.view_type] = (acc[video.view_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calculate average detection rate
    const avgDetectionRate = sessions.length > 0
      ? sessions.reduce((sum, session) => sum + (session.detection_rate || 0), 0) / sessions.length
      : 0;

    // Get date range
    const dates = videos.map(v => v.created_at).sort();

    return {
      totalSessions: sessions.length,
      totalFrames,
      totalVideos: videos.length,
      activityBreakdown,
      viewTypeBreakdown,
      dateRange: {
        earliest: dates[0] || '',
        latest: dates[dates.length - 1] || ''
      },
      averageDetectionRate: avgDetectionRate
    };
  }

  async exportPoseData(options: ExportOptions): Promise<Blob> {
    const data = await this.fetchPoseData(options);

    switch (options.format) {
      case 'csv':
        return this.exportAsCSV(data);
      case 'json':
        return this.exportAsJSON(data);
      case 'parquet':
        // For now, export as JSON with note about parquet conversion
        console.warn('Parquet export not yet implemented, exporting as JSON');
        return this.exportAsJSON(data);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  private async fetchPoseData(options: ExportOptions) {
    // Build comprehensive query to get all related data
    let query = supabase
      .from('pose_data')
      .select(`
        *,
        pose_sessions!inner(
          id,
          video_id,
          model_type,
          analysis_fps,
          detection_rate,
          pose_videos!inner(
            filename,
            activity_type,
            view_type,
            duration,
            width,
            height,
            fps,
            created_at
          )
        )
      `);

    // Apply filters
    if (options.sessionIds && options.sessionIds.length > 0) {
      query = query.in('session_id', options.sessionIds);
    }

    if (options.activityType) {
      query = query.eq('pose_sessions.pose_videos.activity_type', options.activityType);
    }

    if (options.viewType) {
      query = query.eq('pose_sessions.pose_videos.view_type', options.viewType);
    }

    if (options.dateRange) {
      query = query
        .gte('pose_sessions.pose_videos.created_at', options.dateRange.start)
        .lte('pose_sessions.pose_videos.created_at', options.dateRange.end);
    }

    const { data, error } = await query.order('session_id').order('frame_number');

    if (error) {
      throw new Error(`Failed to fetch pose data: ${error.message}`);
    }

    return data || [];
  }

  private exportAsCSV(data: any[]): Blob {
    if (data.length === 0) {
      return new Blob(['No data to export'], { type: 'text/csv' });
    }

    // Define CSV headers
    const headers = [
      'session_id',
      'video_filename',
      'activity_type',
      'view_type',
      'frame_number',
      'timestamp_seconds',
      'pose_detected',
      'detection_confidence',
      'hip_angle',
      'knee_angle',
      'ankle_angle',
      'trunk_angle',
      'neck_angle',
      'hip_x',
      'hip_y',
      'knee_x',
      'knee_y',
      'ankle_x',
      'ankle_y',
      'trunk_x',
      'trunk_y',
      'neck_x',
      'neck_y',
      'stride_length',
      'foot_strike_type',
      'posture_score',
      'video_duration',
      'video_width',
      'video_height',
      'video_fps',
      'analysis_fps',
      'model_type',
      'session_detection_rate'
    ];

    // Convert data to CSV rows
    const rows = data.map(row => {
      const session = row.pose_sessions;
      const video = session.pose_videos;

      return [
        row.session_id,
        video.filename,
        video.activity_type,
        video.view_type,
        row.frame_number,
        row.timestamp_seconds,
        row.pose_detected,
        row.detection_confidence,
        row.hip_angle,
        row.knee_angle,
        row.ankle_angle,
        row.trunk_angle,
        row.neck_angle,
        row.hip_x,
        row.hip_y,
        row.knee_x,
        row.knee_y,
        row.ankle_x,
        row.ankle_y,
        row.trunk_x,
        row.trunk_y,
        row.neck_x,
        row.neck_y,
        row.stride_length,
        row.foot_strike_type,
        row.posture_score,
        video.duration,
        video.width,
        video.height,
        video.fps,
        session.analysis_fps,
        session.model_type,
        session.detection_rate
      ].map(value => {
        // Handle null values and escape commas/quotes
        if (value === null || value === undefined) return '';
        const str = String(value);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      }).join(',');
    });

    const csvContent = [headers.join(','), ...rows].join('\n');
    return new Blob([csvContent], { type: 'text/csv' });
  }

  private exportAsJSON(data: any[]): Blob {
    // Create a more structured JSON format for data science
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        totalRecords: data.length,
        description: 'Pose analysis dataset for biomechanical research'
      },
      sessions: this.groupDataBySessions(data),
      flatData: data.map(row => ({
        // Flatten the nested structure for easier analysis
        session_id: row.session_id,
        video_filename: row.pose_sessions.pose_videos.filename,
        activity_type: row.pose_sessions.pose_videos.activity_type,
        view_type: row.pose_sessions.pose_videos.view_type,
        frame_number: row.frame_number,
        timestamp_seconds: row.timestamp_seconds,
        pose_detected: row.pose_detected,
        detection_confidence: row.detection_confidence,
        joint_angles: {
          hip: row.hip_angle,
          knee: row.knee_angle,
          ankle: row.ankle_angle,
          trunk: row.trunk_angle,
          neck: row.neck_angle
        },
        joint_positions: {
          hip: { x: row.hip_x, y: row.hip_y },
          knee: { x: row.knee_x, y: row.knee_y },
          ankle: { x: row.ankle_x, y: row.ankle_y },
          trunk: { x: row.trunk_x, y: row.trunk_y },
          neck: { x: row.neck_x, y: row.neck_y }
        },
        metrics: {
          stride_length: row.stride_length,
          foot_strike_type: row.foot_strike_type,
          posture_score: row.posture_score
        },
        video_metadata: {
          duration: row.pose_sessions.pose_videos.duration,
          dimensions: {
            width: row.pose_sessions.pose_videos.width,
            height: row.pose_sessions.pose_videos.height
          },
          fps: row.pose_sessions.pose_videos.fps,
          created_at: row.pose_sessions.pose_videos.created_at
        },
        analysis_metadata: {
          model_type: row.pose_sessions.model_type,
          analysis_fps: row.pose_sessions.analysis_fps,
          session_detection_rate: row.pose_sessions.detection_rate
        }
      }))
    };

    const jsonContent = JSON.stringify(exportData, null, 2);
    return new Blob([jsonContent], { type: 'application/json' });
  }

  private groupDataBySessions(data: any[]) {
    const sessions: Record<string, any> = {};

    data.forEach(row => {
      const sessionId = row.session_id;
      if (!sessions[sessionId]) {
        const session = row.pose_sessions;
        const video = session.pose_videos;

        sessions[sessionId] = {
          session_id: sessionId,
          video_metadata: {
            filename: video.filename,
            activity_type: video.activity_type,
            view_type: video.view_type,
            duration: video.duration,
            dimensions: { width: video.width, height: video.height },
            fps: video.fps,
            created_at: video.created_at
          },
          analysis_metadata: {
            model_type: session.model_type,
            analysis_fps: session.analysis_fps,
            detection_rate: session.detection_rate
          },
          frames: []
        };
      }

      sessions[sessionId].frames.push({
        frame_number: row.frame_number,
        timestamp_seconds: row.timestamp_seconds,
        pose_detected: row.pose_detected,
        detection_confidence: row.detection_confidence,
        joint_angles: {
          hip: row.hip_angle,
          knee: row.knee_angle,
          ankle: row.ankle_angle,
          trunk: row.trunk_angle,
          neck: row.neck_angle
        },
        joint_positions: {
          hip: { x: row.hip_x, y: row.hip_y },
          knee: { x: row.knee_x, y: row.knee_y },
          ankle: { x: row.ankle_x, y: row.ankle_y },
          trunk: { x: row.trunk_x, y: row.trunk_y },
          neck: { x: row.neck_x, y: row.neck_y }
        },
        metrics: {
          stride_length: row.stride_length,
          foot_strike_type: row.foot_strike_type,
          posture_score: row.posture_score
        }
      });
    });

    return Object.values(sessions);
  }

  async exportPerformanceMetrics(sessionIds?: string[]): Promise<Blob> {
    let query = supabase
      .from('pose_metrics')
      .select(`
        *,
        pose_sessions!inner(
          id,
          pose_videos!inner(
            filename,
            activity_type,
            view_type,
            created_at
          )
        )
      `);

    if (sessionIds && sessionIds.length > 0) {
      query = query.in('session_id', sessionIds);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch performance metrics: ${error.message}`);
    }

    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        totalMetrics: data?.length || 0,
        description: 'Performance metrics for biomechanical analysis'
      },
      metrics: data || []
    };

    const jsonContent = JSON.stringify(exportData, null, 2);
    return new Blob([jsonContent], { type: 'application/json' });
  }

  async exportRecommendations(sessionIds?: string[]): Promise<Blob> {
    let query = supabase
      .from('pose_recommendations')
      .select(`
        *,
        pose_sessions!inner(
          id,
          pose_videos!inner(
            filename,
            activity_type,
            view_type,
            created_at
          )
        )
      `);

    if (sessionIds && sessionIds.length > 0) {
      query = query.in('session_id', sessionIds);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch recommendations: ${error.message}`);
    }

    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        totalRecommendations: data?.length || 0,
        description: 'AI-generated recommendations for equipment and form optimization'
      },
      recommendations: data || []
    };

    const jsonContent = JSON.stringify(exportData, null, 2);
    return new Blob([jsonContent], { type: 'application/json' });
  }

  downloadBlob(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}
