import * as poseDetection from '@tensorflow-models/pose-detection';
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-backend-webgl';

export interface JointAngle {
  angle: number;
  position: { x: number; y: number };
}

export interface PoseAnalysis {
  angles: {
    hip: JointAngle;
    knee: JointAngle;
    ankle: JointAngle;
    trunk: JointAngle;
    neck: JointAngle;
  };
  metrics: {
    strideLength: number;
    footStrike: string;
    postureScore: number;
  };
}

let detector: poseDetection.PoseDetector | null = null;
let isInitializing = false;
const maxRetries = 3;
const initialRetryDelay = 1000; // Start with 1 second

function getExponentialDelay(retryCount: number): number {
  return Math.min(initialRetryDelay * Math.pow(2, retryCount), 10000); // Cap at 10 seconds
}

export async function initializePoseDetection(retryCount = 0): Promise<poseDetection.PoseDetector> {
  if (detector) return detector;
  if (isInitializing) {
    // Wait for existing initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100));
    return initializePoseDetection(retryCount);
  }

  try {
    isInitializing = true;

    // Ensure TensorFlow is ready and WebGL backend is properly initialized
    await tf.ready();

    // Explicitly set and initialize the WebGL backend
    await tf.setBackend('webgl');

    // Wait for backend initialization
    await tf.ready();

    // Log backend status
    console.log('TensorFlow.js backend:', tf.getBackend());

    // Create detector with correct MediaPipe model configuration
    // Try different model configurations for better compatibility
    try {
      detector = await poseDetection.createDetector(
        poseDetection.SupportedModels.BlazePose,
        {
          runtime: 'tfjs',
          modelType: 'lite',
          enableSmoothing: false, // Disable smoothing for better real-time detection
          enableSegmentation: false
        }
      );
      console.log('BlazePose detector created successfully');
    } catch (blazeError) {
      console.warn('BlazePose failed, trying MoveNet as fallback:', blazeError);
      // Fallback to MoveNet if BlazePose fails
      detector = await poseDetection.createDetector(
        poseDetection.SupportedModels.MoveNet,
        {
          modelType: poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING
        }
      );
      console.log('MoveNet detector created as fallback');
    }

    isInitializing = false;
    return detector;
  } catch (error) {
    isInitializing = false;
    console.error('Error initializing pose detection:', error);

    if (retryCount < maxRetries) {
      const delay = getExponentialDelay(retryCount);
      console.log(`Retrying initialization (attempt ${retryCount + 1}/${maxRetries}) after ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return initializePoseDetection(retryCount + 1);
    }

    throw new Error(`Failed to initialize pose detection after ${maxRetries} attempts: ${error instanceof Error ? error.message : String(error)}`);
  }
}

function calculateAngle(a: number[], b: number[], c: number[]): number {
  const radians = Math.atan2(c[1] - b[1], c[0] - b[0]) -
                 Math.atan2(a[1] - b[1], a[0] - b[0]);
  let angle = Math.abs(radians * 180.0 / Math.PI);

  if (angle > 180.0) {
    angle = 360 - angle;
  }
  return angle;
}

function normalizeCoordinates(x: number, y: number, videoWidth: number, videoHeight: number): { x: number; y: number } {
  // Validate input coordinates
  if (!isFinite(x) || !isFinite(y) || !isFinite(videoWidth) || !isFinite(videoHeight)) {
    console.warn('Invalid coordinates detected:', { x, y, videoWidth, videoHeight });
    return { x: 50, y: 50 }; // Return center as fallback
  }

  // Check if coordinates are already normalized (0-1 range) or in pixel values
  const isAlreadyNormalized = x <= 1 && y <= 1 && x >= 0 && y >= 0;

  let normalizedX: number, normalizedY: number;

  if (isAlreadyNormalized) {
    // Coordinates are already normalized to 0-1, convert to percentage
    normalizedX = x * 100;
    normalizedY = y * 100;
  } else {
    // Coordinates are in pixel values, normalize them
    normalizedX = (x / videoWidth) * 100;
    normalizedY = (y / videoHeight) * 100;
  }

  // Clamp values to reasonable bounds (0-100%)
  normalizedX = Math.max(0, Math.min(100, normalizedX));
  normalizedY = Math.max(0, Math.min(100, normalizedY));

  return {
    x: normalizedX,
    y: normalizedY
  };
}

function getKeypointByName(keypoints: any[], name: string): any | null {
  // Try to detect model type based on keypoint structure
  const isMoveNet = keypoints.length === 17;

  let keypointMap: { [key: string]: number };

  if (isMoveNet) {
    // MoveNet keypoint mapping (17 keypoints)
    keypointMap = {
      'nose': 0,
      'left_eye': 1,
      'right_eye': 2,
      'left_ear': 3,
      'right_ear': 4,
      'left_shoulder': 5,
      'right_shoulder': 6,
      'left_elbow': 7,
      'right_elbow': 8,
      'left_wrist': 9,
      'right_wrist': 10,
      'left_hip': 11,
      'right_hip': 12,
      'left_knee': 13,
      'right_knee': 14,
      'left_ankle': 15,
      'right_ankle': 16,
      // Map missing keypoints to closest alternatives
      'left_foot_index': 15, // Use ankle as fallback
      'right_foot_index': 16 // Use ankle as fallback
    };
  } else {
    // BlazePose keypoint mapping (33 keypoints)
    keypointMap = {
      'nose': 0,
      'left_eye_inner': 1,
      'left_eye': 2,
      'left_eye_outer': 3,
      'right_eye_inner': 4,
      'right_eye': 5,
      'right_eye_outer': 6,
      'left_ear': 7,
      'right_ear': 8,
      'mouth_left': 9,
      'mouth_right': 10,
      'left_shoulder': 11,
      'right_shoulder': 12,
      'left_elbow': 13,
      'right_elbow': 14,
      'left_wrist': 15,
      'right_wrist': 16,
      'left_pinky': 17,
      'right_pinky': 18,
      'left_index': 19,
      'right_index': 20,
      'left_thumb': 21,
      'right_thumb': 22,
      'left_hip': 23,
      'right_hip': 24,
      'left_knee': 25,
      'right_knee': 26,
      'left_ankle': 27,
      'right_ankle': 28,
      'left_heel': 29,
      'right_heel': 30,
      'left_foot_index': 31,
      'right_foot_index': 32
    };
  }

  const index = keypointMap[name];
  if (index !== undefined && keypoints[index]) {
    const keypoint = keypoints[index];
    // Only log important keypoints to reduce console spam
    if (['left_hip', 'right_hip', 'left_knee', 'right_knee'].includes(name)) {
      console.log(`Keypoint ${name} (${isMoveNet ? 'MoveNet' : 'BlazePose'}): score=${keypoint.score?.toFixed(3)}, x=${keypoint.x?.toFixed(1)}, y=${keypoint.y?.toFixed(1)}`);
    }
    if (keypoint.score > 0.1) { // Very low threshold for better detection
      return keypoint;
    }
  }
  return null;
}

export async function analyzePose(source: HTMLVideoElement | HTMLCanvasElement): Promise<PoseAnalysis> {
  try {
    // Check if source is video or canvas and get dimensions accordingly
    let width: number, height: number;

    if (source instanceof HTMLVideoElement) {
      // Enhanced video readiness check
      if (!source.videoWidth || !source.videoHeight) {
        throw new Error('Video dimensions not available - video may not be loaded');
      }

      if (source.readyState < 2) {
        throw new Error('Video not ready for analysis - readyState: ' + source.readyState);
      }

      width = source.videoWidth;
      height = source.videoHeight;

      console.log('Video ready for pose analysis:', {
        width: source.videoWidth,
        height: source.videoHeight,
        currentTime: source.currentTime,
        duration: source.duration,
        readyState: source.readyState,
        paused: source.paused,
        ended: source.ended
      });
    } else {
      // Canvas element
      width = source.width;
      height = source.height;

      if (!width || !height) {
        throw new Error('Canvas dimensions not available');
      }

      console.log('Canvas ready for pose analysis:', {
        width: source.width,
        height: source.height
      });
    }

    if (!detector) {
      console.log('Detector not initialized, initializing now...');
      detector = await initializePoseDetection();
    }

    // Create a tensor from the source element for validation
    const sourceTensor = tf.browser.fromPixels(source);
    console.log('Source tensor shape:', sourceTensor.shape);

    if (!sourceTensor.shape || sourceTensor.shape[0] === 0 || sourceTensor.shape[1] === 0) {
      sourceTensor.dispose();
      throw new Error('Invalid source dimensions for pose analysis');
    }

    // Clean up tensor immediately after validation
    sourceTensor.dispose();

    console.log('Estimating poses with detector...');
    const startTime = performance.now();
    const poses = await detector.estimatePoses(source, {
      maxPoses: 1,
      flipHorizontal: false
    });
    const endTime = performance.now();

    console.log(`Pose estimation took ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`Detected ${poses.length} poses`);

    if (poses.length === 0) {
      console.log('No poses detected - this could be due to:');
      console.log('1. No person visible in the current frame');
      console.log('2. Person is too small or partially occluded');
      console.log('3. Poor lighting or video quality');
      console.log('4. Model confidence threshold too high');
      throw new Error('No pose detected in current frame');
    }

    const pose = poses[0];
    const keypoints = pose.keypoints;

    console.log(`Pose has ${keypoints?.length || 0} keypoints`);

    if (!keypoints || keypoints.length === 0) {
      throw new Error('No keypoints detected in pose');
    }

    // Detect model type and log keypoint scores for debugging
    const isMoveNet = keypoints.length === 17;
    console.log(`Using ${isMoveNet ? 'MoveNet' : 'BlazePose'} model with ${keypoints.length} keypoints`);

    const importantKeypoints = ['left_hip', 'right_hip', 'left_knee', 'right_knee', 'left_ankle', 'right_ankle'];
    console.log('Important keypoint scores:');
    importantKeypoints.forEach(name => {
      const kp = getKeypointByName(keypoints, name);
      console.log(`  ${name}: ${kp ? kp.score.toFixed(3) : 'not detected'}`);
    });

    // Get dimensions for coordinate normalization
    const videoWidth = width;
    const videoHeight = height;

    // Get key body points using helper function
    const leftShoulder = getKeypointByName(keypoints, 'left_shoulder');
    const rightShoulder = getKeypointByName(keypoints, 'right_shoulder');
    const leftHip = getKeypointByName(keypoints, 'left_hip');
    const rightHip = getKeypointByName(keypoints, 'right_hip');
    const leftKnee = getKeypointByName(keypoints, 'left_knee');
    const rightKnee = getKeypointByName(keypoints, 'right_knee');
    const leftAnkle = getKeypointByName(keypoints, 'left_ankle');
    const rightAnkle = getKeypointByName(keypoints, 'right_ankle');
    const leftFoot = getKeypointByName(keypoints, 'left_foot_index');
    const rightFoot = getKeypointByName(keypoints, 'right_foot_index');
    const nose = getKeypointByName(keypoints, 'nose');

    // Use the most visible side (higher confidence scores)
    const useLeftSide = (leftHip?.score || 0) > (rightHip?.score || 0);
    console.log(`Using ${useLeftSide ? 'left' : 'right'} side for analysis`);
    console.log(`Left hip score: ${leftHip?.score || 0}, Right hip score: ${rightHip?.score || 0}`);

    const hip = useLeftSide ? leftHip : rightHip;
    const knee = useLeftSide ? leftKnee : rightKnee;
    const ankle = useLeftSide ? leftAnkle : rightAnkle;
    const foot = useLeftSide ? leftFoot : rightFoot;
    const shoulder = useLeftSide ? leftShoulder : rightShoulder;

    console.log('Selected keypoints:', { hip, knee, ankle, shoulder, nose });

    // Calculate joint angles with proper error handling
    let hipAngle = 0;
    let kneeAngle = 0;
    let ankleAngle = 0;
    let trunkAngle = 0;
    let neckAngle = 0;

    if (hip && knee && ankle) {
      hipAngle = calculateAngle(
        [shoulder?.x || hip.x, shoulder?.y || hip.y - 50], // shoulder or estimated point above hip
        [hip.x, hip.y],
        [knee.x, knee.y]
      );
    }

    if (hip && knee && ankle) {
      kneeAngle = calculateAngle(
        [hip.x, hip.y],
        [knee.x, knee.y],
        [ankle.x, ankle.y]
      );
    }

    if (knee && ankle && foot) {
      ankleAngle = calculateAngle(
        [knee.x, knee.y],
        [ankle.x, ankle.y],
        [foot?.x || ankle.x, foot?.y || ankle.y + 20] // foot or estimated point below ankle
      );
    }

    if (shoulder && hip) {
      // Calculate trunk angle relative to vertical
      const deltaX = hip.x - shoulder.x;
      const deltaY = hip.y - shoulder.y;
      trunkAngle = Math.abs(Math.atan2(deltaX, deltaY) * 180 / Math.PI);
    }

    if (nose && shoulder) {
      // Calculate neck angle
      const deltaX = shoulder.x - nose.x;
      const deltaY = shoulder.y - nose.y;
      neckAngle = Math.abs(Math.atan2(deltaX, deltaY) * 180 / Math.PI);
    }

    // Debug coordinate values before normalization
    if (hip) {
      console.log('Raw hip coordinates:', { x: hip.x, y: hip.y, videoWidth, videoHeight });
    }
    if (knee) {
      console.log('Raw knee coordinates:', { x: knee.x, y: knee.y, videoWidth, videoHeight });
    }

    // Normalize coordinates for overlay positioning
    const normalizedHip = hip ? normalizeCoordinates(hip.x, hip.y, videoWidth, videoHeight) : { x: 50, y: 50 };
    const normalizedKnee = knee ? normalizeCoordinates(knee.x, knee.y, videoWidth, videoHeight) : { x: 50, y: 70 };
    const normalizedAnkle = ankle ? normalizeCoordinates(ankle.x, ankle.y, videoWidth, videoHeight) : { x: 50, y: 90 };
    const normalizedShoulder = shoulder ? normalizeCoordinates(shoulder.x, shoulder.y, videoWidth, videoHeight) : { x: 50, y: 30 };
    const normalizedNose = nose ? normalizeCoordinates(nose.x, nose.y, videoWidth, videoHeight) : { x: 50, y: 10 };

    // Debug normalized coordinates
    console.log('Normalized coordinates:', {
      hip: normalizedHip,
      knee: normalizedKnee,
      ankle: normalizedAnkle,
      shoulder: normalizedShoulder,
      nose: normalizedNose
    });

    // Calculate basic metrics
    const strideLength = hip && ankle ? Math.abs(hip.x - ankle.x) / videoWidth * 2.0 : 1.32; // Rough estimation
    const postureScore = Math.max(0, Math.min(100, 100 - trunkAngle)); // Better posture = lower trunk angle

    // Return analysis results
    return {
      angles: {
        hip: {
          angle: hipAngle,
          position: normalizedHip
        },
        knee: {
          angle: kneeAngle,
          position: normalizedKnee
        },
        ankle: {
          angle: ankleAngle,
          position: normalizedAnkle
        },
        trunk: {
          angle: trunkAngle,
          position: normalizedShoulder
        },
        neck: {
          angle: neckAngle,
          position: normalizedNose
        }
      },
      metrics: {
        strideLength: Math.round(strideLength * 100) / 100,
        footStrike: ankleAngle > 95 ? 'heel' : ankleAngle < 85 ? 'forefoot' : 'midfoot',
        postureScore: Math.round(postureScore)
      }
    };
  } catch (error) {
    console.error('Error in analyzePose:', error);
    throw error;
  }
}