# Pre-Processing & Data Science Guide

## Overview

The Video Analysis Tool now includes comprehensive pre-processing capabilities designed for data science research and biomechanical analysis. This guide explains how to use these features effectively.

## Getting Started

### 1. Database Setup

First, set up the database schema in your Supabase project:

1. Open your Supabase SQL Editor
2. Run the migration file: `supabase/migrations/20250521120000_pose_analysis_tables.sql`
3. Verify tables are created: `pose_videos`, `pose_sessions`, `pose_data`, `pose_metrics`, `pose_recommendations`

### 2. Navigation

The application now has three main sections accessible via the top navigation:

- **Analysis**: Real-time video analysis (original functionality)
- **Pre-Process**: Offline batch processing for data science
- **Data**: Dataset management and export tools

## Pre-Processing Pipeline

### Features

- **Batch Processing**: Process multiple videos simultaneously
- **Frame-by-Frame Analysis**: Extract pose data from every frame
- **Configurable Frame Rate**: Analyze at 1-10 FPS to balance detail vs. processing time
- **Progress Tracking**: Real-time progress monitoring
- **Error Handling**: Robust error recovery and reporting

### How to Use

1. **Navigate to Pre-Process tab**
2. **Configure Analysis Settings**:
   - Select activity type (Running/Cycling)
   - Choose camera view (Side/Rear)
3. **Upload Videos**:
   - Drag and drop multiple video files
   - Supports MP4, MOV, AVI, WebM formats
   - Portrait videos recommended
4. **Start Processing**:
   - Click "Start Processing" to begin batch analysis
   - Monitor progress for each video
   - Processing continues in background

### Output Data

Each processed video generates:

- **Video Record**: Metadata and processing status
- **Analysis Session**: Configuration and summary statistics
- **Pose Data**: Frame-by-frame joint angles and positions
- **Performance Metrics**: Calculated biomechanical measurements
- **Recommendations**: AI-generated equipment suggestions

## Data Management

### Dataset Overview

The Data Management dashboard provides:

- **Dataset Statistics**: Total sessions, frames, videos, detection rates
- **Activity Breakdown**: Distribution by running/cycling
- **View Type Analysis**: Side vs. rear view statistics
- **Timeline Information**: Date range of collected data

### Export Options

#### Export Formats

1. **CSV Format**:
   - Spreadsheet-compatible
   - Ideal for statistical analysis
   - Flattened data structure
   - All metrics in columns

2. **JSON Format**:
   - Structured data format
   - Ideal for machine learning
   - Hierarchical organization
   - Metadata included

#### Export Types

1. **Pose Data**: Frame-by-frame analysis results
2. **Performance Metrics**: Calculated biomechanical measurements
3. **Recommendations**: AI-generated suggestions

#### Filtering Options

- **Activity Type**: Running or cycling only
- **View Type**: Side or rear view only
- **Date Range**: Specific time periods
- **Session IDs**: Specific analysis sessions

## Data Structure

### Pose Data Fields

Each frame contains:

```json
{
  "session_id": "uuid",
  "frame_number": 123,
  "timestamp_seconds": 4.1,
  "pose_detected": true,
  "detection_confidence": 0.85,
  "joint_angles": {
    "hip": 145.2,
    "knee": 128.7,
    "ankle": 93.1,
    "trunk": 12.5,
    "neck": 8.2
  },
  "joint_positions": {
    "hip": {"x": 45.2, "y": 67.8},
    "knee": {"x": 42.1, "y": 78.9},
    "ankle": {"x": 41.5, "y": 89.2}
  },
  "metrics": {
    "stride_length": 1.32,
    "foot_strike_type": "midfoot",
    "posture_score": 78
  }
}
```

### Performance Metrics

Calculated metrics include:

- **Stride Consistency**: Coefficient of variation in stride length
- **Knee Drive Efficiency**: Optimal knee angle scoring
- **Posture Stability**: Trunk angle consistency
- **Foot Strike Pattern**: Distribution of heel/midfoot/forefoot strikes

## Data Science Applications

### Machine Learning

The exported datasets are ready for:

- **Classification Models**: Predicting optimal equipment
- **Regression Analysis**: Performance prediction
- **Time Series Analysis**: Movement pattern evolution
- **Clustering**: Identifying movement archetypes

### Research Applications

- **Biomechanical Studies**: Gait and cycling form analysis
- **Equipment Testing**: Correlating gear with movement patterns
- **Performance Optimization**: Identifying efficiency improvements
- **Injury Prevention**: Detecting risk patterns

### Example Analysis Workflows

#### 1. Running Shoe Recommendation Model

```python
# Load pose data
df = pd.read_csv('pose_data_export.csv')

# Feature engineering
features = ['avg_hip_angle', 'avg_knee_angle', 'stride_length', 'foot_strike_type']
X = df[features]
y = df['recommended_shoe_type']

# Train model
model = RandomForestClassifier()
model.fit(X, y)
```

#### 2. Cycling Aerodynamics Analysis

```python
# Analyze torso angle vs. power efficiency
cycling_data = df[df['activity_type'] == 'cycling']
correlation = cycling_data['trunk_angle'].corr(cycling_data['efficiency_score'])
```

## Best Practices

### Video Quality

- **Good Lighting**: Ensure clear visibility of joints
- **Stable Camera**: Minimize camera shake
- **Full Body Visible**: Keep entire body in frame
- **Consistent Background**: Avoid cluttered backgrounds

### Processing Efficiency

- **Batch Processing**: Process multiple videos together
- **Optimal Frame Rate**: Use 5-10 FPS for most applications
- **Regular Exports**: Export data regularly to avoid loss

### Data Management

- **Consistent Naming**: Use descriptive filenames
- **Regular Backups**: Export and backup datasets
- **Version Control**: Track analysis configurations
- **Documentation**: Record analysis parameters and goals

## Troubleshooting

### Common Issues

1. **Low Detection Rate**:
   - Check video quality and lighting
   - Ensure full body is visible
   - Try different camera angles

2. **Processing Failures**:
   - Verify video format compatibility
   - Check file size limits
   - Monitor browser memory usage

3. **Export Issues**:
   - Check browser download settings
   - Verify sufficient disk space
   - Try smaller date ranges

### Performance Optimization

- **Close Other Tabs**: Free up browser memory
- **Process in Batches**: Avoid processing too many videos simultaneously
- **Monitor Progress**: Watch for stuck or failed jobs

## Support

For technical support or questions about the pre-processing features:

1. Check the browser console for error messages
2. Verify database connectivity
3. Review video format compatibility
4. Contact the development team with specific error details

## Future Enhancements

Planned improvements include:

- **Parquet Export**: More efficient data format for large datasets
- **Real-time Streaming**: Live pose analysis during recording
- **Advanced Metrics**: Additional biomechanical calculations
- **API Access**: Programmatic access to processing pipeline
- **Cloud Processing**: Server-side processing for large datasets
